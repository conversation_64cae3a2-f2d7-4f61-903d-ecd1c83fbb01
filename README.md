# Flux - Projets de Développement

Ce dépôt contient des projets de développement, avec une focalisation sur les extensions de navigateur.

## Amazon Review Insights

**Version actuelle:** 0.2.0

### Description

Une extension Chrome qui utilise l'intelligence artificielle pour analyser les avis Amazon et présenter un résumé des points positifs et négatifs les plus mentionnés. Cette extension ajoute un badge interactif à côté des boutons "Ajouter au panier" dans les résultats de recherche Amazon et les pages de produit.

### Fonctionnalités Principales

- **Badges interactifs**: Ajoute un badge "R" à côté des boutons d'achat sur Amazon
- **Analyse IA**: Traite les avis avec l'API OpenRouter (modèle DeepSeek R1)
- **Résumés concis**: Identifie les 3 points positifs et négatifs les plus cités
- **Multi-langues**: Prise en charge du français et de l'anglais
- **Mise en cache**: Stockage des analyses pendant 24h pour optimiser les performances
- **Interface adaptative**: Style cohérent avec le design d'Amazon, compatible mode sombre
- **Système de logs**: Suivi détaillé des opérations pour le débogage

### Architecture Technique

#### Scripts Principaux

- **service_worker.js**: Cœur de l'extension qui gère l'extraction des avis et l'analyse IA
- **inject.js**: Script injecté dans les pages Amazon pour ajouter les badges et tooltips
- **common.js**: Système de logging centralisé utilisé par tous les composants
- **popup.js**: Interface de configuration accessible depuis l'icône de l'extension

#### Flux d'Exécution

1. L'extension injecte le script dans les pages de recherche Amazon
2. Des badges sont ajoutés à côté des boutons "Ajouter au panier"
3. Au clic sur un badge, le service worker extrait jusqu'à 5 pages d'avis Amazon
4. Les avis sont envoyés à l'API OpenRouter/DeepSeek pour analyse
5. Les résultats sont affichés dans un tooltip et mis en cache

#### API et Services Externes

- **OpenRouter API**: Analyse des avis via le modèle DeepSeek R1
- Clé API: `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`

#### Structure du Projet

```
amazon-review-insights/
├── common.js             # Système de logs partagé
├── config.js             # Configuration globale
├── create_icon.js        # Générateur d'icônes
├── manifest.json         # Manifeste de l'extension
├── package.json          # Dépendances
├── service_worker.js     # Service worker principal
├── content/
│   ├── inject.js         # Script injecté
│   ├── style.css         # Styles des badges et tooltips
│   └── lib/              # Bibliothèques
├── icons/
│   └── 48.png            # Icône de l'extension
├── popup/
│   ├── popup.html        # Interface de configuration
│   └── popup.js          # Logique de configuration
└── ui/
    └── tooltip.html      # Modèle de tooltip
```

### Installation et Développement

**Prérequis:**
- Node.js v14+
- npm ou yarn

**Installation:**
```bash
cd amazon-review-insights
npm install
```

**Développement:**
```bash
npm run dev
```

**Production:**
```bash
npm run build
```

**Chargement de l'extension:**
1. Ouvrir Chrome > Extensions > Mode développeur
2. Cliquer sur "Charger l'extension non empaquetée"
3. Sélectionner le dossier "amazon-review-insights"

### Fonctionnalités Techniques Avancées

- **Extraction robuste**: Plusieurs méthodes de fallback pour trouver les avis
- **Détection d'ASIN**: Algorithme avancé pour identifier l'identifiant unique des produits Amazon
- **Observateur de mutations**: Détecte les changements DOM pour ajouter des badges dynamiquement
- **Gestion des tooltips**: Interface interactive avec suivi de l'état des actions utilisateur
- **Analyse parallèle**: Jusqu'à 5 analyses peuvent être exécutées simultanément
- **Mode dégradé**: Fallbacks pour gestion des erreurs et des cas limites

## Vite Build Configuration

Le projet utilise Vite pour la compilation, configuré via le fichier `vite.config.ts` à la racine du dépôt. Cette configuration:

- Utilise Vue.js
- Prend en charge le hot-reloading des extensions
- Organise la compilation en plusieurs entrées (popup, content, background, etc.)
- Copie statiquement le manifest.json dans le dossier de sortie

### Bibliothèques Utilisées

- **canvas**: Génération d'icônes
- **hot-reload-extension-vite**: Rechargement à chaud des extensions pendant le développement

### Configuration d'Environnement

Le projet prend en charge deux environnements:
- **Développement**: Avec logs détaillés et rechargement à chaud
- **Production**: Logs limités aux erreurs critiques