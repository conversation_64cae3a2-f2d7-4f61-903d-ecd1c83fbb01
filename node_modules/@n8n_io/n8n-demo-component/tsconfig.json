{"compilerOptions": {"target": "es2019", "module": "es2020", "lib": ["es2020", "DOM", "DOM.Iterable"], "declaration": true, "declarationMap": true, "sourceMap": true, "inlineSources": true, "outDir": "./", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "plugins": [{"name": "ts-lit-plugin", "strict": true}]}, "include": ["src/**/*.ts"], "exclude": []}