---
layout: page.11ty.cjs
title: <n8n-demo> ⌲ Install
---

# Install

`<n8n-demo>` is distributed on npm, so you can install it locally or use it via npm CDNs like unpkg.com.

```html
<script src="https://cdn.jsdelivr.net/npm/@webcomponents/webcomponentsjs@2.0.0/webcomponents-loader.js"></script>
<script src="https://www.unpkg.com/lit@2.0.0-rc.2/polyfill-support.js"></script>
<script type="module" src="https://cdn.jsdelivr.net/npm/@n8n_io/n8n-demo-component/n8n-demo.bundled.js"></script>
```

After adding these scripts, you can now directly use the component within HTML.