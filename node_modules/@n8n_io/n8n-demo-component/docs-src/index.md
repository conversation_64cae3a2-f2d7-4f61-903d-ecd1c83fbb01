---
layout: page.11ty.cjs
title: <n8n-demo> ⌲ Home
---

# &lt;n8n-demo>

`<n8n-demo>` is a web component to render workflow previews.

## As easy as HTML

<section class="columns">
  <div>

`<n8n-demo>` is just an HTML element. You can it anywhere you can use HTML!

```html
<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}'></n8n-demo>
```

  </div>
  <div>

<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}'></n8n-demo>

  </div>
</section>

## Configure with attributes

<section class="columns">
  <div>

`<n8n-demo>` can be configured with attributed in plain HTML.

```html
<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame=true></n8n-demo>
```

  </div>
  <div>

<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame=true></n8n-demo>

  </div>
</section>

## Declarative rendering

<section class="columns">
  <div>

`<n8n-demo>` can be used with declarative rendering libraries like Angular, React, Vue, and lit-html

```js
import {html, render} from 'lit-html';
const workflow = '{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}';

render(
  html`
    <h2>This is a &lt;n8n-demo&gt;</h2>
    <n8n-demo .workflow=${workflow}></n8n-demo>
  `,
  document.body
);
```

  </div>
  <div>

<h2>This is a &lt;n8n-demo&gt;</h2>
<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame=true></n8n-demo>

  </div>
</section>
