---
layout: example.11ty.cjs
title: <n8n-demo> ⌲ Examples ⌲ Customzie
tags: example
name: Customize
description: Customize styles
---

<style>
  n8n-demo {
	--n8n-frame-background-color: black;
	--n8n-json-background-color: lightgray;
	--n8n-copy-button-background-color: gray;
	--n8n-workflow-min-height: 500px;
	--n8n-iframe-border-radius: 30px;
  }
</style>
<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame="true"></n8n-demo>

<h3>CSS</h3>

```css
n8n-demo {
	--n8n-frame-background-color: black;
	--n8n-json-background-color: lightgray;
	--n8n-copy-button-background-color: gray;
	--n8n-workflow-min-height: 500px;
	--n8n-iframe-border-radius: 30px
}
```

<h3>HTML</h3>

```html
<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame="true"></n8n-demo>
```
