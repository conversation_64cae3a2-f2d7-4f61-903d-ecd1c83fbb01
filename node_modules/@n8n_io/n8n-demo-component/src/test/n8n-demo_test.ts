import {N8NDemo} from '../n8n-demo.js';

import {fixture, assert} from '@open-wc/testing';
import {html} from 'lit/static-html.js';

suite('n8n-demo', () => {
  test('is defined', () => {
    const el = document.createElement('n8n-demo');
    assert.instanceOf(el, N8NDemo);
  });

  test('renders with default values', async () => {
    const el = await fixture(html`<n8n-demo></n8n-demo>`);
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
      </div>
			`
    );
  });

  test('renders with theme', async () => {
    const el = await fixture(html`<n8n-demo theme="dark"></n8n-demo>`);
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo?theme=dark"
          >
          </iframe>
        </div>
      </div>
			`
    );
  });

  test('renders with custom src', async () => {
    const el = await fixture(
      html`<n8n-demo src="https://www.n8n.io" theme="dark"></n8n-demo>`
    );
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://www.n8n.io?theme=dark"
          >
          </iframe>
        </div>
      </div>
			`
    );
  });

  test('renders with overlay and non-interactive canvas', async () => {
    const el = await fixture(
      html`<n8n-demo clicktointeract="true"></n8n-demo>`
    );
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow">
        <div class="canvas-container">
          <div
            class="overlay"
            hidden=""
          >
            <button>
              Click to explore
            </button>
          </div>
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe non_interactive"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
      </div>
			`
    );
  });

  test('renders with frame', async () => {
    const el = await fixture(html`<n8n-demo frame="true"></n8n-demo>`);
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow frame">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
        <div class="embedded_tip">
          💡 Double-click a node to see its settings, or paste
          <button class="code_toggle">
            this workflow's code
          </button>
          into n8n to import it
        </div>
      </div>
			`
    );
  });

  test('renders with frame, overlay and non-interactive canvas', async () => {
    const el = await fixture(
      html`<n8n-demo frame="true" clicktointeract="true"></n8n-demo>`
    );
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow frame">
        <div class="canvas-container">
          <div
            class="overlay"
            hidden=""
          >
            <button>
              Click to explore
            </button>
          </div>
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe non_interactive"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
        <div class="embedded_tip">
          💡 Double-click a node to see its settings, or paste
          <button class="code_toggle">
            this workflow's code
          </button>
          into n8n to import it
        </div>
      </div>
			`
    );
  });

  test('expands json on click', async () => {
    const el = (await fixture(
      html`<n8n-demo
        workflow="%7B%22nodes%22%3A%20%5B%5D%7D"
        frame="true"
      ></n8n-demo>`
    )) as N8NDemo;
    const button = el.shadowRoot!.querySelector('.embedded_tip > button')!;
    if (button instanceof HTMLElement) {
      button.click();
    }
    await el.updateComplete;
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow frame">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
        <div class="embedded_tip embedded_tip_with_code">
          💡 Double-click a node to see its settings, or paste
          <button class="code_toggle">
            this workflow's code
          </button>
          into n8n to import it
        </div>
        <div class="workflow_json">
          <div class="copy_button">
            Copy
          </div>
          <pre
            class="json_renderer"
            id="json"
          >
            {
  "nodes": []
}
          </pre>
        </div>
      </div>
    	`
    );
  });

  test('handles invalid json when expanding', async () => {
    const el = (await fixture(
      html`<n8n-demo workflow="invalid" frame="true"></n8n-demo>`
    )) as N8NDemo;
    const button = el.shadowRoot!.querySelector('.embedded_tip > button')!;
    if (button instanceof HTMLElement) {
      button.click();
    }
    await el.updateComplete;
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow frame">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
        <div class="embedded_tip embedded_tip_with_code">
          💡 Double-click a node to see its settings, or paste
          <button class="code_toggle">
            this workflow's code
          </button>
          into n8n to import it
        </div>
        <div class="workflow_json">
          <div class="copy_button">
            Copy
          </div>
          <pre
            class="json_renderer"
            id="json"
          >
            Invalid JSON
          </pre>
        </div>
      </div>
    	`
    );
  });

  test('canvas is interactive after click ', async () => {
    const el = (await fixture(
      html`<n8n-demo clicktointeract="true"></n8n-demo>`
    )) as N8NDemo;
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow">
        <div class="canvas-container">
          <div
            class="overlay"
            hidden=""
          >
            <button>
              Click to explore
            </button>
          </div>
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe non_interactive"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
      </div>
    	`
    );

    const button = el.shadowRoot!.querySelector('.overlay > button')!;
    if (button instanceof HTMLElement) {
      button.click();
    }
    await el.updateComplete;
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
      </div>
    	`
    );
  });

  test('when disableinteractivity=true renders with frame, overlay and non-interactive canvas', async () => {
    const el = await fixture(
      html`<n8n-demo
        frame="true"
        disableinteractivity="true"
        clicktointeract="true"
      ></n8n-demo>`
    );
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow frame">
        <div class="canvas-container">
          <div
            class="overlay"
            hidden=""
          >
          </div>
          <iframe
            class="embedded_workflow_iframe non_interactive"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
        <div class="embedded_tip">
          💡 Double-click a node to see its settings, or paste
          <button class="code_toggle">
            this workflow's code
          </button>
          into n8n to import it
        </div>
      </div>
			`
    );
  });

  test('renders workflow correctly with escaped quotes', async () => {
    const workflow = {nodes: [{ name: "\"test\""}]};
    const el = (await fixture(
      html`<n8n-demo
        workflow="${JSON.stringify(workflow)}"
        frame="true"
      ></n8n-demo>`
    )) as N8NDemo;
    const button = el.shadowRoot!.querySelector('.embedded_tip > button')!;
    if (button instanceof HTMLElement) {
      button.click();
    }
    await el.updateComplete;
    assert.shadowDom.equal(
      el,
      `
      <div class="embedded_workflow frame">
        <div class="canvas-container">
          <iframe
            allow="clipboard-write"
            class="embedded_workflow_iframe"
            id="int_iframe"
            src="https://n8n-preview-service.internal.n8n.cloud/workflows/demo"
          >
          </iframe>
        </div>
        <div class="embedded_tip embedded_tip_with_code">
          💡 Double-click a node to see its settings, or paste
          <button class="code_toggle">
            this workflow's code
          </button>
          into n8n to import it
        </div>
        <div class="workflow_json">
          <div class="copy_button">
            Copy
          </div>
          <pre
            class="json_renderer"
            id="json"
          >
            {
  "nodes": [
    {
      "name": "\\"test\\""
    }
  ]
}
          </pre>
        </div>
      </div>
    	`
    );
  });
});
