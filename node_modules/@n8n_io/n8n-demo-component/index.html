<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <title>&lt;n8n-demo> Demo</title>
    <script src="/node_modules/@webcomponents/webcomponentsjs/webcomponents-loader.js"></script>
    <script src="/node_modules/lit/polyfill-support.js"></script>
    <script type="module" src="/n8n-demo.js"></script>
  </head>
  <body>
    <h2>Options</h2>
    <input type="checkbox" id="frame" checked />
    <label for="frame">frame</label>
    <input type="checkbox" id="collapseformobile" checked />
    <label for="collapseformobile">collapseformobile</label>
    <input type="checkbox" id="clicktointeract" checked />
    <label for="clicktointeract">clicktointeract</label>
    <input type="checkbox" id="disableinteractivity" />
    <label for="disableinteractivity">disableinteractivity</label>
    <input type="checkbox" id="hidecanvaserrors" />
    <label for="hidecanvaserrors">hidecanvaserrors</label>
    <input type="checkbox" id="error" />
    <label for="error">error</label>

    <br /><br /><br />
    <h2>Preview</h2>
    <div id="output"></div>

    <script>
      var workflow = JSON.stringify({"meta":{"instanceId":"cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"},"nodes":[{"parameters":{},"name":"Start","type":"n8n-nodes-base.start","typeVersion":1,"position":[400,780],"id":"2bc07f94-166a-4913-9c4e-19975279cff5"},{"parameters":{"conditions":{"string":[{"value1":"={{$json[\"body\"][\"token\"]}}","operation":"notEqual","value2":"54iz37xumjg9ue6bo8ygqifb8y"}]}},"name":"IF","type":"n8n-nodes-base.if","typeVersion":1,"position":[800,780],"id":"a5954a39-9680-456b-bea4-b333c4523e07"},{"parameters":{"authentication":"oAuth2","resource":"file","operation":"edit","owner":"mutdmour","repository":"={{$json[\"repo\"]}}","filePath":"Dockerfile","fileContent":"=FROM node:14.16-alpine\n\n# pass N8N_VERSION Argument while building or use default\nARG N8N_VERSION=0.98.0\n\n# Update everything and install needed dependencies\nRUN apk add --update graphicsmagick tzdata\n\n# Set a custom user to not have n8n run as root\nUSER root\n\nRUN node --version\n\n# Install n8n and the also temporary all the packages\n# it needs to build it correctly.\n# RUN apk --update add --virtual build-dependencies python build-base && \\\n# \tnpm_config_user=root npm install -g n8n@${N8N_VERSION} && \\\n# \tapk del build-dependencies\n\nRUN apk --update add --virtual build-dependencies python2 python3 build-base && \\\n\tapk --update add git && \\\n\tapk del build-dependencies\n\nRUN N8N_CORE_BRANCH={{$json[\"branch\"]}} && \\\n    git clone https://github.com/n8n-io/n8n && \\\n\tcd n8n && \\\n    echo $N8N_CORE_BRANCH && \\\n    git fetch origin $N8N_CORE_BRANCH && \\\n    git checkout $N8N_CORE_BRANCH && \\\n\tnpm install -g typescript && \\\n\tnpm install -g lerna && \\\n\tnpm install && \\\n\tlerna bootstrap --hoist && \\\n\tnpm_config_user=root npm run build \n\n# Specifying work directory\nWORKDIR /data\n\n# copy start script to container\nCOPY ./start.sh /\n\n# make the script executable\nRUN chmod +x /start.sh\n\n# define execution entrypoint\nCMD [\"/start.sh\"]","commitMessage":"=n8n bot - deploy branch {{$json[\"branch\"]}}"},"name":"GitHub","type":"n8n-nodes-base.github","typeVersion":1,"position":[1360,960],"id":"0cdca3a1-6c06-46b5-a905-50987efc8ab7"},{"parameters":{"functionCode":"const responseUrl = items[0].json.body.response_url;\nconst text = items[0].json.body.text;\nconst [todeploy, branch] = text.split();\nconst instances = todeploy.split();\nreturn Array.from(new Set(instances)).map((name) => ({\n  json: {\n    name,\n    repo: `n8n-heroku-${name}`,\n    branch,\n    responseUrl,\n    instanceUrl: `https://n8n-${name}.herokuapp.com/`,\n    username: name,\n    password: test1234\n  }\n}));\n"},"name":"Function1","type":"n8n-nodes-base.function","typeVersion":1,"position":[1000,910],"id":"de7def62-752c-4ff3-8142-aa300db53a08"},{"parameters":{},"name":"NoOp1","type":"n8n-nodes-base.noOp","typeVersion":1,"position":[1150,1000],"id":"5923e63c-1861-4fbd-8b6e-5b658ed5417a"},{"parameters":{"mode":"passThrough","output":"input2"},"name":"Merge","type":"n8n-nodes-base.merge","typeVersion":1,"position":[1410,1250],"id":"9d7853f3-e457-4e39-ae82-f6163a706c5a"},{"parameters":{"requestMethod":"POST","url":"={{$json[\"responseUrl\"]}}","responseFormat":"string","options":{},"bodyParametersUi":{"parameter":[{"name":"text","value":"=Updated {{$json[\"repo\"]}} with \"{{$json[\"branch\"]}}\" branch. Should take effect in 10 or so minutes.\nYou can follow its progress here https://github.com/mutdmour/n8n-heroku-{{$json[\"username\"]}}/deployments/activity_log?environment=n8n-{{$json[\"username\"]}}\n\nURL: {{$json[\"instanceUrl\"]}}\nusername: {{$json[\"username\"]}}\npassword: {{$json[\"password\"]}}"}]}},"name":"HTTP Request","type":"n8n-nodes-base.httpRequest","typeVersion":1,"position":[1610,1250],"id":"195e655c-5fd1-4d19-9b26-73958daf1eb9"},{"parameters":{"httpMethod":"POST","path":"5b44d7e0-0221-4886-a416-0070ac8cae67","options":{}},"name":"Webhook","type":"n8n-nodes-base.webhook","typeVersion":1,"position":[580,770],"webhookId":"5b44d7e0-0221-4886-a416-0070ac8cae67","id":"984a7b48-6366-4558-af62-6ee6a8a7c61b"},{"parameters":{"otherOptions":{}},"id":"6d998f7c-564b-4e57-9c04-22749641269f","name":"Slack","type":"n8n-nodes-base.slack","typeVersion":2.1,"position":[1040,640]}],"connections":{"IF":{"main":[[{"node":"Slack","type":"main","index":0}],[{"node":"Function1","type":"main","index":0}]]},"GitHub":{"main":[[{"node":"Merge","type":"main","index":0}]]},"Function1":{"main":[[{"node":"NoOp1","type":"main","index":0}]]},"NoOp1":{"main":[[{"node":"GitHub","type":"main","index":0},{"node":"Merge","type":"main","index":1}]]},"Merge":{"main":[[{"node":"HTTP Request","type":"main","index":0}]]},"Webhook":{"main":[[{"node":"IF","type":"main","index":0}]]}},"pinData":{}});
      var container = document.getElementById('output');
      var n8nDemo = document.querySelector('n8n-demo');
      var errorEl = document.getElementById('error');

      var options = [
        'frame',
        'collapseformobile',
        'clicktointeract',
        'disableinteractivity',
        'hidecanvaserrors'
      ];
      options.forEach((option) => {
        var checkbox = document.getElementById(option);
        checkbox.addEventListener('change', reRender);
      });

      errorEl.addEventListener('change', reRender);

      function reRender() {
        if (n8nDemo) {
          n8nDemo.remove();
        }
        n8nDemo = document.createElement('n8n-demo');
        for (var option of options) {
          n8nDemo.setAttribute(option, document.getElementById(option).checked);
        }
        n8nDemo.setAttribute(
          'workflow',
          document.getElementById('error').checked ? '{"test":1}' : workflow
        );

        container.appendChild(n8nDemo);
      }

      reRender();
    </script>

    <br />
    Below text is added to make testing scrolling behavior easy...

    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy
    eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam
    voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam
    nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed
    diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet
    clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit
    amet.
  </body>
</html>
