
<!doctype html>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><n8n-demo> ⌲ Docs</title>
    <link rel="stylesheet" href="../docs.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600|Roboto+Mono">
    <link href="../prism-okaidia.css" rel="stylesheet" />
    <script src="/node_modules/@webcomponents/webcomponentsjs/webcomponents-loader.js"></script>
    <script src="/node_modules/lit/polyfill-support.js"></script>
    <script type="module" src="../n8n-demo.bundled.js"></script>
  </head>
  <body>
    
<header>
  <h1>&lt;n8n-demo></h1>
  <h2>Workflow preview web component</h2>
</header>
    
<nav>
  <a href="../">Home</a>
  <a href="../examples/">Examples</a>
  <a href="">API</a>
  <a href="../install/">Install</a>
</nav>
    <div id="main-wrapper">
      <main>
        
     <h1>API</h1>
     
       <h2>&lt;n8n-demo></h2>
       <div>
         
       </div>
       
   <h3>Attributes</h3>
   <table>
     <tr>
       <th>Name</th><th>Description</th><th>Type</th><th>Default</th>
     </tr>
     
       <tr>
         <td>workflow</td><td>Workflow json to load.</td><td>string</td><td>'{}'</td>
       </tr>
     
       <tr>
         <td>frame</td><td>Whether to add frame around canvas with code and copy button</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>src</td><td>URL for n8n instance to load workflow.</td><td>string</td><td>'https://n8n-preview-service.internal.n8n.cloud/workflows/demo'</td>
       </tr>
     
       <tr>
         <td>collapseformobile</td><td>Whether to collapse on mobile, so that scrolling on mobile is easier.</td><td>string</td><td>'true'</td>
       </tr>
     
       <tr>
         <td>clicktointeract</td><td>Add button before users can interact with canvas.
Makes scrolling through page easier without getting bugged down.</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>hidecanvaserrors</td><td>Hide node errors on the canvas</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>disableinteractivity</td><td>Disable interactivity entirely. This will prevent the user from
interacting with the workflow.</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>theme</td><td>Whether to force a theme on n8n.
Accepts 'light' and 'dark'</td><td></td><td>undefined</td>
       </tr>
     
   </table>
 
       
   <h3>Properties</h3>
   <table>
     <tr>
       <th>Name</th><th>Attribute</th><th>Description</th><th>Type</th><th>Default</th>
     </tr>
     
       <tr>
         <td>workflow</td><td>workflow</td><td>Workflow json to load.</td><td>string</td><td>'{}'</td>
       </tr>
     
       <tr>
         <td>frame</td><td>frame</td><td>Whether to add frame around canvas with code and copy button</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>src</td><td>src</td><td>URL for n8n instance to load workflow.</td><td>string</td><td>'https://n8n-preview-service.internal.n8n.cloud/workflows/demo'</td>
       </tr>
     
       <tr>
         <td>collapseformobile</td><td>collapseformobile</td><td>Whether to collapse on mobile, so that scrolling on mobile is easier.</td><td>string</td><td>'true'</td>
       </tr>
     
       <tr>
         <td>clicktointeract</td><td>clicktointeract</td><td>Add button before users can interact with canvas.
Makes scrolling through page easier without getting bugged down.</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>hidecanvaserrors</td><td>hidecanvaserrors</td><td>Hide node errors on the canvas</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>disableinteractivity</td><td>disableinteractivity</td><td>Disable interactivity entirely. This will prevent the user from
interacting with the workflow.</td><td>string</td><td>'false'</td>
       </tr>
     
       <tr>
         <td>theme</td><td>theme</td><td>Whether to force a theme on n8n.
Accepts 'light' and 'dark'</td><td></td><td>undefined</td>
       </tr>
     
       <tr>
         <td>showCode</td><td></td><td></td><td>boolean</td><td>false</td>
       </tr>
     
       <tr>
         <td>showPreview</td><td></td><td></td><td>boolean</td><td>true</td>
       </tr>
     
       <tr>
         <td>fullscreen</td><td></td><td></td><td>boolean</td><td>false</td>
       </tr>
     
       <tr>
         <td>insideIframe</td><td></td><td></td><td>boolean</td><td>false</td>
       </tr>
     
       <tr>
         <td>copyText</td><td></td><td></td><td>string</td><td>'Copy'</td>
       </tr>
     
       <tr>
         <td>isMobileView</td><td></td><td></td><td>boolean</td><td>false</td>
       </tr>
     
       <tr>
         <td>error</td><td></td><td></td><td>boolean</td><td>false</td>
       </tr>
     
       <tr>
         <td>interactive</td><td></td><td></td><td>boolean</td><td>true</td>
       </tr>
     
       <tr>
         <td>scrollX</td><td></td><td></td><td>number</td><td>0</td>
       </tr>
     
       <tr>
         <td>scrollY</td><td></td><td></td><td>number</td><td>0</td>
       </tr>
     
       <tr>
         <td>receiveMessage</td><td></td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>onDocumentScroll</td><td></td><td></td><td></td><td></td>
       </tr>
     
   </table>
   
       
   <h3>Methods</h3>
   <table>
     <tr>
       <th>Name</th><th>Parameters</th><th>Description</th><th>Return</th>
     </tr>
     
       <tr>
         <td>loadWorkflow</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>toggleCode</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>onMouseEnter</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>onMouseLeave</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>onOverlayClick</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>copyClipboard</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>toggleView</td><td></td><td></td><td></td>
       </tr>
     
       <tr>
         <td>renderIframe</td><td></td><td></td><td></td>
       </tr>
     
   </table>
 
           
         
       
       
       
   
      </main>
    </div>
    
<footer>
</footer>
  </body>
</html>