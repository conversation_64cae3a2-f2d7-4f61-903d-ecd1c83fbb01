
<!doctype html>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><n8n-demo> ⌲ Home</title>
    <link rel="stylesheet" href="docs.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600|Roboto+Mono">
    <link href="prism-okaidia.css" rel="stylesheet" />
    <script src="/node_modules/@webcomponents/webcomponentsjs/webcomponents-loader.js"></script>
    <script src="/node_modules/lit/polyfill-support.js"></script>
    <script type="module" src="n8n-demo.bundled.js"></script>
  </head>
  <body>
    
<header>
  <h1>&lt;n8n-demo></h1>
  <h2>Workflow preview web component</h2>
</header>
    
<nav>
  <a href="">Home</a>
  <a href="examples/">Examples</a>
  <a href="api/">API</a>
  <a href="install/">Install</a>
</nav>
    <div id="main-wrapper">
      <main>
        <h1>&lt;n8n-demo&gt;</h1>
<p><code>&lt;n8n-demo&gt;</code> is a web component to render workflow previews.</p>
<h2>As easy as HTML</h2>
<section class="columns">
  <div>
<p><code>&lt;n8n-demo&gt;</code> is just an HTML element. You can it anywhere you can use HTML!</p>
<pre class="language-html"><code class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>n8n-demo</span> <span class="token attr-name">workflow</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{<span class="token punctuation">"</span>nodes<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Workflow-Created<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[512,369],<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>path<span class="token punctuation">"</span>:<span class="token punctuation">"</span>webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>httpMethod<span class="token punctuation">"</span>:<span class="token punctuation">"</span>POST<span class="token punctuation">"</span>},<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1}],<span class="token punctuation">"</span>connections<span class="token punctuation">"</span>:{}}<span class="token punctuation">'</span></span><span class="token punctuation">></span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>n8n-demo</span><span class="token punctuation">></span></span></code></pre>
  </div>
  <div>
<p><n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}'></n8n-demo></p>
  </div>
</section>
<h2>Configure with attributes</h2>
<section class="columns">
  <div>
<p><code>&lt;n8n-demo&gt;</code> can be configured with attributed in plain HTML.</p>
<pre class="language-html"><code class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>n8n-demo</span> <span class="token attr-name">workflow</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{<span class="token punctuation">"</span>nodes<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Workflow-Created<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[512,369],<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>path<span class="token punctuation">"</span>:<span class="token punctuation">"</span>webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>httpMethod<span class="token punctuation">"</span>:<span class="token punctuation">"</span>POST<span class="token punctuation">"</span>},<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1}],<span class="token punctuation">"</span>connections<span class="token punctuation">"</span>:{}}<span class="token punctuation">'</span></span> <span class="token attr-name">frame</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span>true</span><span class="token punctuation">></span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>n8n-demo</span><span class="token punctuation">></span></span></code></pre>
  </div>
  <div>
<p><n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame=true></n8n-demo></p>
  </div>
</section>
<h2>Declarative rendering</h2>
<section class="columns">
  <div>
<p><code>&lt;n8n-demo&gt;</code> can be used with declarative rendering libraries like Angular, React, Vue, and lit-html</p>
<pre class="language-js"><code class="language-js"><span class="token keyword">import</span> <span class="token punctuation">{</span>html<span class="token punctuation">,</span> render<span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">'lit-html'</span><span class="token punctuation">;</span><br><span class="token keyword">const</span> workflow <span class="token operator">=</span> <span class="token string">'{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}'</span><span class="token punctuation">;</span><br><br><span class="token function">render</span><span class="token punctuation">(</span><br>  html<span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string"><br>    &lt;h2>This is a &amp;lt;n8n-demo&amp;gt;&lt;/h2><br>    &lt;n8n-demo .workflow=</span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>workflow<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">>&lt;/n8n-demo><br>  </span><span class="token template-punctuation string">`</span></span><span class="token punctuation">,</span><br>  document<span class="token punctuation">.</span>body<br><span class="token punctuation">)</span><span class="token punctuation">;</span></code></pre>
  </div>
  <div>
<h2>This is a &lt;n8n-demo&gt;</h2>
<n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame=true></n8n-demo>
  </div>
</section>

      </main>
    </div>
    
<footer>
</footer>
  </body>
</html>