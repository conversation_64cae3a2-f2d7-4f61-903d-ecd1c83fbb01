
<!doctype html>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><n8n-demo> ⌲ Examples ⌲ Frame Property</title>
    <link rel="stylesheet" href="../../docs.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600|Roboto+Mono">
    <link href="../../prism-okaidia.css" rel="stylesheet" />
    <script src="/node_modules/@webcomponents/webcomponentsjs/webcomponents-loader.js"></script>
    <script src="/node_modules/lit/polyfill-support.js"></script>
    <script type="module" src="../../n8n-demo.bundled.js"></script>
  </head>
  <body>
    
<header>
  <h1>&lt;n8n-demo></h1>
  <h2>Workflow preview web component</h2>
</header>
    
<nav>
  <a href="../../">Home</a>
  <a href="../">Examples</a>
  <a href="../../api/">API</a>
  <a href="../../install/">Install</a>
</nav>
    <div id="main-wrapper">
      <main>
        
    <h1>Example: Frame Property</h1>
    <section class="examples">
      <nav class="collection">
        <ul>
          
                  <li class=>
                    <a href="../click-to-interact/">Click to interact example</a>
                  </li>
                
                  <li class=>
                    <a href="../customize-styles/">Customize styles</a>
                  </li>
                
                  <li class=selected>
                    <a href="">With code section</a>
                  </li>
                
                  <li class=>
                    <a href="../">Basic example (without code section)</a>
                  </li>
                
                  <li class=>
                    <a href="../override-theme/">Override theme</a>
                  </li>
                
                  <li class=>
                    <a href="../disable-interactivity/">Disable interactivity example</a>
                  </li>
                
        </ul>
      </nav>
      <div>
        <p><n8n-demo workflow='{"nodes":[{"parameters":{},"name":"Start","type":"n8n-nodes-base.start","typeVersion":1,"position":[250,300]},{"parameters":{"conditions":{"string":[{"value1":"=","operation":"notEqual","value2":"54iz37xumjg9ue6bo8ygqifb8y"}]}},"name":"IF","type":"n8n-nodes-base.if","typeVersion":1,"position":[650,300]},{"parameters":{},"name":"NoOp","type":"n8n-nodes-base.noOp","typeVersion":1,"position":[850,160]},{"parameters":{"authentication":"oAuth2","resource":"file","operation":"edit","owner":"mutdmour","repository":"=","filePath":"Dockerfile","fileContent":"=FROM node:14.16-alpine\n\n# pass N8N_VERSION Argument while building or use default\nARG N8N_VERSION=0.98.0\n\n# Update everything and install needed dependencies\nRUN apk add --update graphicsmagick tzdata\n\n# Set a custom user to not have n8n run as root\nUSER root\n\nRUN node --version\n\n# Install n8n and the also temporary all the packages\n# it needs to build it correctly.\n# RUN apk --update add --virtual build-dependencies python build-base && \\\n# \tnpm_config_user=root npm install -g n8n@${N8N_VERSION} && \\\n# \tapk del build-dependencies\n\nRUN apk --update add --virtual build-dependencies python2 python3 build-base && \\\n\tapk --update add git && \\\n\tapk del build-dependencies\n\nRUN N8N_CORE_BRANCH= && \\\n    git clone https://github.com/n8n-io/n8n && \\\n\tcd n8n && \\\n    echo $N8N_CORE_BRANCH && \\\n    git fetch origin $N8N_CORE_BRANCH && \\\n    git checkout $N8N_CORE_BRANCH && \\\n\tnpm install -g typescript && \\\n\tnpm install -g lerna && \\\n\tnpm install && \\\n\tlerna bootstrap --hoist && \\\n\tnpm_config_user=root npm run build \n\n# Specifying work directory\nWORKDIR /data\n\n# copy start script to container\nCOPY ./start.sh /\n\n# make the script executable\nRUN chmod +x /start.sh\n\n# define execution entrypoint\nCMD [\"/start.sh\"]","commitMessage":"=n8n bot - deploy branch "},"name":"GitHub","type":"n8n-nodes-base.github","typeVersion":1,"position":[1210,480],"credentials":{"githubOAuth2Api":{"id":"40","name":"Github account"}}},{"parameters":{"functionCode":"const responseUrl = items[0].json.body.response_url;\nconst text = items[0].json.body.text;\nconst [todeploy, branch] = text.split();\nconst instances = todeploy.split();\nreturn Array.from(new Set(instances)).map((name) => ({\n  json: {\n    name,\n    repo: `n8n-heroku-${name}`,\n    branch,\n    responseUrl,\n    instanceUrl: `https://n8n-${name}.herokuapp.com/`,\n    username: name,\n    password: test1234\n  }\n}));\n"},"name":"Function1","type":"n8n-nodes-base.function","typeVersion":1,"position":[850,430]},{"parameters":{},"name":"NoOp1","type":"n8n-nodes-base.noOp","typeVersion":1,"position":[1000,520]},{"parameters":{"mode":"passThrough","output":"input2"},"name":"Merge","type":"n8n-nodes-base.merge","typeVersion":1,"position":[1260,770]},{"parameters":{"requestMethod":"POST","url":"=","responseFormat":"string","options":{},"bodyParametersUi":{"parameter":[{"name":"text","value":"=Updated  with \"\" branch. Should take effect in 10 or so minutes.\nYou can follow its progress here https://github.com/mutdmour/n8n-heroku-/deployments/activity_log?environment=n8n-\n\nURL: \nusername: \npassword: "}]}},"name":"HTTP Request","type":"n8n-nodes-base.httpRequest","typeVersion":1,"position":[1460,770]},{"parameters":{"httpMethod":"POST","path":"5b44d7e0-0221-4886-a416-0070ac8cae67","options":{}},"name":"Webhook","type":"n8n-nodes-base.webhook","typeVersion":1,"position":[430,290],"webhookId":"5b44d7e0-0221-4886-a416-0070ac8cae67"}],"connections":{"IF":{"main":[[{"node":"NoOp","type":"main","index":0}],[{"node":"Function1","type":"main","index":0}]]},"GitHub":{"main":[[{"node":"Merge","type":"main","index":0}]]},"Function1":{"main":[[{"node":"NoOp1","type":"main","index":0}]]},"NoOp1":{"main":[[{"node":"GitHub","type":"main","index":0},{"node":"Merge","type":"main","index":1}]]},"Merge":{"main":[[{"node":"HTTP Request","type":"main","index":0}]]},"Webhook":{"main":[[{"node":"IF","type":"main","index":0}]]}}}' frame=true></n8n-demo></p>
<h3>HTML</h3>
<pre class="language-html"><code class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>n8n-demo</span> <span class="token attr-name">workflow</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{<span class="token punctuation">"</span>nodes<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Start<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.start<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[250,300]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>conditions<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>string<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>value1<span class="token punctuation">"</span>:<span class="token punctuation">"</span>=<span class="token punctuation">"</span>,<span class="token punctuation">"</span>operation<span class="token punctuation">"</span>:<span class="token punctuation">"</span>notEqual<span class="token punctuation">"</span>,<span class="token punctuation">"</span>value2<span class="token punctuation">"</span>:<span class="token punctuation">"</span>54iz37xumjg9ue6bo8ygqifb8y<span class="token punctuation">"</span>}]}},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>IF<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.if<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[650,300]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>NoOp<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.noOp<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[850,160]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>authentication<span class="token punctuation">"</span>:<span class="token punctuation">"</span>oAuth2<span class="token punctuation">"</span>,<span class="token punctuation">"</span>resource<span class="token punctuation">"</span>:<span class="token punctuation">"</span>file<span class="token punctuation">"</span>,<span class="token punctuation">"</span>operation<span class="token punctuation">"</span>:<span class="token punctuation">"</span>edit<span class="token punctuation">"</span>,<span class="token punctuation">"</span>owner<span class="token punctuation">"</span>:<span class="token punctuation">"</span>mutdmour<span class="token punctuation">"</span>,<span class="token punctuation">"</span>repository<span class="token punctuation">"</span>:<span class="token punctuation">"</span>=<span class="token punctuation">"</span>,<span class="token punctuation">"</span>filePath<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Dockerfile<span class="token punctuation">"</span>,<span class="token punctuation">"</span>fileContent<span class="token punctuation">"</span>:<span class="token punctuation">"</span>=FROM node:14.16-alpine\n\n# pass N8N_VERSION Argument while building or use default\nARG N8N_VERSION=0.98.0\n\n# Update everything and install needed dependencies\nRUN apk add --update graphicsmagick tzdata\n\n# Set a custom user to not have n8n run as root\nUSER root\n\nRUN node --version\n\n# Install n8n and the also temporary all the packages\n# it needs to build it correctly.\n# RUN apk --update add --virtual build-dependencies python build-base &amp;&amp; \\\n# \tnpm_config_user=root npm install -g n8n@${N8N_VERSION} &amp;&amp; \\\n# \tapk del build-dependencies\n\nRUN apk --update add --virtual build-dependencies python2 python3 build-base &amp;&amp; \\\n\tapk --update add git &amp;&amp; \\\n\tapk del build-dependencies\n\nRUN N8N_CORE_BRANCH= &amp;&amp; \\\n    git clone https://github.com/n8n-io/n8n &amp;&amp; \\\n\tcd n8n &amp;&amp; \\\n    echo $N8N_CORE_BRANCH &amp;&amp; \\\n    git fetch origin $N8N_CORE_BRANCH &amp;&amp; \\\n    git checkout $N8N_CORE_BRANCH &amp;&amp; \\\n\tnpm install -g typescript &amp;&amp; \\\n\tnpm install -g lerna &amp;&amp; \\\n\tnpm install &amp;&amp; \\\n\tlerna bootstrap --hoist &amp;&amp; \\\n\tnpm_config_user=root npm run build \n\n# Specifying work directory\nWORKDIR /data\n\n# copy start script to container\nCOPY ./start.sh /\n\n# make the script executable\nRUN chmod +x /start.sh\n\n# define execution entrypoint\nCMD [\<span class="token punctuation">"</span>/start.sh\<span class="token punctuation">"</span>]<span class="token punctuation">"</span>,<span class="token punctuation">"</span>commitMessage<span class="token punctuation">"</span>:<span class="token punctuation">"</span>=n8n bot - deploy branch <span class="token punctuation">"</span>},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>GitHub<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.github<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[1210,480],<span class="token punctuation">"</span>credentials<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>githubOAuth2Api<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>id<span class="token punctuation">"</span>:<span class="token punctuation">"</span>40<span class="token punctuation">"</span>,<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Github account<span class="token punctuation">"</span>}}},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>functionCode<span class="token punctuation">"</span>:<span class="token punctuation">"</span>const responseUrl = items[0].json.body.response_url;\nconst text = items[0].json.body.text;\nconst [todeploy, branch] = text.split();\nconst instances = todeploy.split();\nreturn Array.from(new Set(instances)).map((name) => ({\n  json: {\n    name,\n    repo: `n8n-heroku-${name}`,\n    branch,\n    responseUrl,\n    instanceUrl: `https://n8n-${name}.herokuapp.com/`,\n    username: name,\n    password: test1234\n  }\n}));\n<span class="token punctuation">"</span>},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Function1<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.function<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[850,430]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>NoOp1<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.noOp<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[1000,520]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>mode<span class="token punctuation">"</span>:<span class="token punctuation">"</span>passThrough<span class="token punctuation">"</span>,<span class="token punctuation">"</span>output<span class="token punctuation">"</span>:<span class="token punctuation">"</span>input2<span class="token punctuation">"</span>},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Merge<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.merge<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[1260,770]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>requestMethod<span class="token punctuation">"</span>:<span class="token punctuation">"</span>POST<span class="token punctuation">"</span>,<span class="token punctuation">"</span>url<span class="token punctuation">"</span>:<span class="token punctuation">"</span>=<span class="token punctuation">"</span>,<span class="token punctuation">"</span>responseFormat<span class="token punctuation">"</span>:<span class="token punctuation">"</span>string<span class="token punctuation">"</span>,<span class="token punctuation">"</span>options<span class="token punctuation">"</span>:{},<span class="token punctuation">"</span>bodyParametersUi<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>parameter<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>text<span class="token punctuation">"</span>,<span class="token punctuation">"</span>value<span class="token punctuation">"</span>:<span class="token punctuation">"</span>=Updated  with \<span class="token punctuation">"</span>\<span class="token punctuation">"</span> branch. Should take effect in 10 or so minutes.\nYou can follow its progress here https://github.com/mutdmour/n8n-heroku-/deployments/activity_log?environment=n8n-\n\nURL: \nusername: \npassword: <span class="token punctuation">"</span>}]}},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>HTTP Request<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.httpRequest<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[1460,770]},{<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>httpMethod<span class="token punctuation">"</span>:<span class="token punctuation">"</span>POST<span class="token punctuation">"</span>,<span class="token punctuation">"</span>path<span class="token punctuation">"</span>:<span class="token punctuation">"</span>5b44d7e0-0221-4886-a416-0070ac8cae67<span class="token punctuation">"</span>,<span class="token punctuation">"</span>options<span class="token punctuation">"</span>:{}},<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[430,290],<span class="token punctuation">"</span>webhookId<span class="token punctuation">"</span>:<span class="token punctuation">"</span>5b44d7e0-0221-4886-a416-0070ac8cae67<span class="token punctuation">"</span>}],<span class="token punctuation">"</span>connections<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>IF<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>main<span class="token punctuation">"</span>:[[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>NoOp<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0}],[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Function1<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0}]]},<span class="token punctuation">"</span>GitHub<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>main<span class="token punctuation">"</span>:[[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Merge<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0}]]},<span class="token punctuation">"</span>Function1<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>main<span class="token punctuation">"</span>:[[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>NoOp1<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0}]]},<span class="token punctuation">"</span>NoOp1<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>main<span class="token punctuation">"</span>:[[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>GitHub<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0},{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Merge<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:1}]]},<span class="token punctuation">"</span>Merge<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>main<span class="token punctuation">"</span>:[[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>HTTP Request<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0}]]},<span class="token punctuation">"</span>Webhook<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>main<span class="token punctuation">"</span>:[[{<span class="token punctuation">"</span>node<span class="token punctuation">"</span>:<span class="token punctuation">"</span>IF<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>main<span class="token punctuation">"</span>,<span class="token punctuation">"</span>index<span class="token punctuation">"</span>:0}]]}}}<span class="token punctuation">'</span></span> <span class="token attr-name">frame</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span>true</span><span class="token punctuation">></span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>n8n-demo</span><span class="token punctuation">></span></span></code></pre>

      </div>
    </section>
  
      </main>
    </div>
    
<footer>
</footer>
  </body>
</html>