
<!doctype html>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><n8n-demo> ⌲ Examples ⌲ Overrie Theme</title>
    <link rel="stylesheet" href="../../docs.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600|Roboto+Mono">
    <link href="../../prism-okaidia.css" rel="stylesheet" />
    <script src="/node_modules/@webcomponents/webcomponentsjs/webcomponents-loader.js"></script>
    <script src="/node_modules/lit/polyfill-support.js"></script>
    <script type="module" src="../../n8n-demo.bundled.js"></script>
  </head>
  <body>
    
<header>
  <h1>&lt;n8n-demo></h1>
  <h2>Workflow preview web component</h2>
</header>
    
<nav>
  <a href="../../">Home</a>
  <a href="../">Examples</a>
  <a href="../../api/">API</a>
  <a href="../../install/">Install</a>
</nav>
    <div id="main-wrapper">
      <main>
        
    <h1>Example: Override Theme</h1>
    <section class="examples">
      <nav class="collection">
        <ul>
          
                  <li class=>
                    <a href="../click-to-interact/">Click to interact example</a>
                  </li>
                
                  <li class=>
                    <a href="../customize-styles/">Customize styles</a>
                  </li>
                
                  <li class=>
                    <a href="../frame-property/">With code section</a>
                  </li>
                
                  <li class=>
                    <a href="../">Basic example (without code section)</a>
                  </li>
                
                  <li class=selected>
                    <a href="">Override theme</a>
                  </li>
                
                  <li class=>
                    <a href="../disable-interactivity/">Disable interactivity example</a>
                  </li>
                
        </ul>
      </nav>
      <div>
        <p><n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' theme="dark"></n8n-demo></p>
<h3>HTML</h3>
<pre class="language-html"><code class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>n8n-demo</span> <span class="token attr-name">workflow</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{<span class="token punctuation">"</span>nodes<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Workflow-Created<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[512,369],<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>path<span class="token punctuation">"</span>:<span class="token punctuation">"</span>webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>httpMethod<span class="token punctuation">"</span>:<span class="token punctuation">"</span>POST<span class="token punctuation">"</span>},<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1}],<span class="token punctuation">"</span>connections<span class="token punctuation">"</span>:{}}<span class="token punctuation">'</span></span> <span class="token attr-name">theme</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>dark<span class="token punctuation">"</span></span><span class="token punctuation">></span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>n8n-demo</span><span class="token punctuation">></span></span></code></pre>

      </div>
    </section>
  
      </main>
    </div>
    
<footer>
</footer>
  </body>
</html>