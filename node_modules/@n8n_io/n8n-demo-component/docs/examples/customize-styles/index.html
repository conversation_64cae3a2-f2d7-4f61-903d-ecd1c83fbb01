
<!doctype html>

<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><n8n-demo> ⌲ Examples ⌲ Customzie</title>
    <link rel="stylesheet" href="../../docs.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600|Roboto+Mono">
    <link href="../../prism-okaidia.css" rel="stylesheet" />
    <script src="/node_modules/@webcomponents/webcomponentsjs/webcomponents-loader.js"></script>
    <script src="/node_modules/lit/polyfill-support.js"></script>
    <script type="module" src="../../n8n-demo.bundled.js"></script>
  </head>
  <body>
    
<header>
  <h1>&lt;n8n-demo></h1>
  <h2>Workflow preview web component</h2>
</header>
    
<nav>
  <a href="../../">Home</a>
  <a href="../">Examples</a>
  <a href="../../api/">API</a>
  <a href="../../install/">Install</a>
</nav>
    <div id="main-wrapper">
      <main>
        
    <h1>Example: Customize</h1>
    <section class="examples">
      <nav class="collection">
        <ul>
          
                  <li class=>
                    <a href="../click-to-interact/">Click to interact example</a>
                  </li>
                
                  <li class=selected>
                    <a href="">Customize styles</a>
                  </li>
                
                  <li class=>
                    <a href="../frame-property/">With code section</a>
                  </li>
                
                  <li class=>
                    <a href="../">Basic example (without code section)</a>
                  </li>
                
                  <li class=>
                    <a href="../override-theme/">Override theme</a>
                  </li>
                
                  <li class=>
                    <a href="../disable-interactivity/">Disable interactivity example</a>
                  </li>
                
        </ul>
      </nav>
      <div>
        <style>
  n8n-demo {
	--n8n-frame-background-color: black;
	--n8n-json-background-color: lightgray;
	--n8n-copy-button-background-color: gray;
	--n8n-workflow-min-height: 500px;
	--n8n-iframe-border-radius: 30px;
  }
</style>
<p><n8n-demo workflow='{"nodes":[{"name":"Workflow-Created","type":"n8n-nodes-base.webhook","position":[512,369],"parameters":{"path":"webhook","httpMethod":"POST"},"typeVersion":1}],"connections":{}}' frame="true"></n8n-demo></p>
<h3>CSS</h3>
<pre class="language-css"><code class="language-css"><span class="token selector">n8n-demo</span> <span class="token punctuation">{</span><br>	<span class="token property">--n8n-frame-background-color</span><span class="token punctuation">:</span> black<span class="token punctuation">;</span><br>	<span class="token property">--n8n-json-background-color</span><span class="token punctuation">:</span> lightgray<span class="token punctuation">;</span><br>	<span class="token property">--n8n-copy-button-background-color</span><span class="token punctuation">:</span> gray<span class="token punctuation">;</span><br>	<span class="token property">--n8n-workflow-min-height</span><span class="token punctuation">:</span> 500px<span class="token punctuation">;</span><br>	<span class="token property">--n8n-iframe-border-radius</span><span class="token punctuation">:</span> 30px<br><span class="token punctuation">}</span></code></pre>
<h3>HTML</h3>
<pre class="language-html"><code class="language-html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>n8n-demo</span> <span class="token attr-name">workflow</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">'</span>{<span class="token punctuation">"</span>nodes<span class="token punctuation">"</span>:[{<span class="token punctuation">"</span>name<span class="token punctuation">"</span>:<span class="token punctuation">"</span>Workflow-Created<span class="token punctuation">"</span>,<span class="token punctuation">"</span>type<span class="token punctuation">"</span>:<span class="token punctuation">"</span>n8n-nodes-base.webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>position<span class="token punctuation">"</span>:[512,369],<span class="token punctuation">"</span>parameters<span class="token punctuation">"</span>:{<span class="token punctuation">"</span>path<span class="token punctuation">"</span>:<span class="token punctuation">"</span>webhook<span class="token punctuation">"</span>,<span class="token punctuation">"</span>httpMethod<span class="token punctuation">"</span>:<span class="token punctuation">"</span>POST<span class="token punctuation">"</span>},<span class="token punctuation">"</span>typeVersion<span class="token punctuation">"</span>:1}],<span class="token punctuation">"</span>connections<span class="token punctuation">"</span>:{}}<span class="token punctuation">'</span></span> <span class="token attr-name">frame</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">></span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>n8n-demo</span><span class="token punctuation">></span></span></code></pre>

      </div>
    </section>
  
      </main>
    </div>
    
<footer>
</footer>
  </body>
</html>