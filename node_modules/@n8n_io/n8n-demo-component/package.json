{"name": "@n8n_io/n8n-demo-component", "version": "1.0.18", "description": "web component for workflow previews", "main": "n8n-demo.js", "module": "n8n-demo.js", "type": "module", "scripts": {"build": "tsc; rollup -c", "build:watch": "tsc --watch", "clean": "rimraf n8n-demo.{d.ts,d.ts.map,js,js.map} test/n8n-demo.{d.ts,d.ts.map,js,js.map} test/n8n-demo_test.{d.ts,d.ts.map,js,js.map}", "lint": "npm run lint:lit-analyzer && npm run lint:eslint", "lint:eslint": "eslint 'src/**/*.ts'", "lint:lit-analyzer": "lit-analyzer", "format": "prettier \"**/*.{cjs,html,js,json,md,ts}\" --ignore-path ./.es<PERSON><PERSON><PERSON> --write", "docs": "npm run docs:clean && npm run build && npm run analyze && npm run docs:build && npm run docs:assets && npm run docs:gen", "docs:clean": "<PERSON><PERSON><PERSON> docs", "docs:gen": "eleventy --config=.eleventy.cjs", "docs:gen:watch": "eleventy --config=.eleventy.cjs --watch", "docs:build": "rollup -c --file docs/n8n-demo.bundled.js", "docs:assets": "cp node_modules/prismjs/themes/prism-okaidia.css docs/", "docs:serve": "wds --root-dir=docs --node-resolve --watch", "analyze": "cem analyze --litelement --globs \"src/**/*.ts\"", "analyze:watch": "cem analyze --litelement --globs \"src/**/*.ts\" --watch", "serve": "wds --watch", "serve:prod": "MODE=prod npm run serve", "test": "npm run test:dev && npm run test:prod", "test:dev": "wtr", "test:watch": "wtr --watch", "test:prod": "MODE=prod wtr", "test:prod:watch": "MODE=prod wtr --watch", "checksize": "rollup -c ; cat n8n-demo.bundled.js | gzip -9 | wc -c ; rm n8n-demo.bundled.js"}, "keywords": ["web-components", "lit-element", "typescript", "lit"], "author": "Bearbobs", "dependencies": {"lit": "^2.0.0"}, "devDependencies": {"@11ty/eleventy": "^0.12.1", "@11ty/eleventy-plugin-syntaxhighlight": "^3.0.1", "@custom-elements-manifest/analyzer": "^0.5.3", "@esm-bundle/chai": "^4.1.5", "@open-wc/testing": "^3.0.0-next.1", "@open-wc/testing-karma": "^4.0.9", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^3.0.0", "@types/mocha": "^9.0.0", "@typescript-eslint/eslint-plugin": "^4.3.0", "@typescript-eslint/parser": "^4.30.0", "@web/dev-server": "^0.1.22", "@web/dev-server-legacy": "^0.1.4", "@web/dev-server-rollup": "^0.3.9", "@web/test-runner": "^0.13.16", "@web/test-runner-mocha": "^0.7.4", "@web/test-runner-playwright": "^0.8.4", "@webcomponents/webcomponentsjs": "^2.6.0", "deepmerge": "^4.2.2", "eslint": "^7.32.0", "lit-analyzer": "^1.1.10", "mocha": "^9.1.1", "prettier": "^2.3.2", "rimraf": "^3.0.2", "rollup": "^2.28.2", "rollup-plugin-summary": "^1.2.3", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.3.5"}, "customElements": "custom-elements.json", "types": "./n8n-demo.d.ts", "directories": {"doc": "docs", "test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/n8n-io/n8n-demo-webcomponent.git"}, "license": "ISC", "bugs": {"url": "https://github.com/n8n-io/n8n-demo-webcomponent/issues"}, "homepage": "https://github.com/n8n-io/n8n-demo-webcomponent#readme"}