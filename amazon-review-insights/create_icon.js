const fs = require('fs');
const { createCanvas } = require('canvas');

// Créer un canvas de 48x48 pixels
const canvas = createCanvas(48, 48);
const ctx = canvas.getContext('2d');

// Fond blanc
ctx.fillStyle = '#FFFFFF';
ctx.fillRect(0, 0, 48, 48);

// Dessiner une bulle de commentaire
ctx.fillStyle = '#0088AA';
ctx.beginPath();
ctx.moveTo(8, 8);
ctx.lineTo(40, 8);
ctx.quadraticCurveTo(44, 8, 44, 12);
ctx.lineTo(44, 30);
ctx.quadraticCurveTo(44, 34, 40, 34);
ctx.lineTo(28, 34);
ctx.lineTo(24, 40);
ctx.lineTo(20, 34);
ctx.lineTo(8, 34);
ctx.quadraticCurveTo(4, 34, 4, 30);
ctx.lineTo(4, 12);
ctx.quadraticCurveTo(4, 8, 8, 8);
ctx.fill();

// Sauvegarder l'image
const buffer = canvas.toBuffer('image/png');
fs.mkdirSync('icons', { recursive: true });
fs.writeFileSync('icons/48.png', buffer);