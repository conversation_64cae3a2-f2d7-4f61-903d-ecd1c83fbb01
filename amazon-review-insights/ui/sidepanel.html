<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Amazon Review Insights - Analy<PERSON> de <PERSON></title>
  <!-- RechartsJS pour les visualisations -->
  <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
  <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
  <script src="https://unpkg.com/recharts@2.1.9/umd/Recharts.min.js"></script>
  <style>
    :root {
      --primary-color: #232F3E;
      --accent-color: #FF9900;
      --text-color: #333;
      --light-text: #767676;
      --border-color: #DDD;
      --hover-color: #F5F5F5;
      --error-color: #D32F2F;
      --success-color: #4CAF50;
      --warning-color: #F9A825;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      color: var(--text-color);
      font-size: 14px;
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }

    .container {
      padding: 16px;
    }

    .search-bar {
      display: flex;
      margin-bottom: 16px;
      gap: 8px;
    }

    .search-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 14px;
    }

    .search-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 12px;
      cursor: pointer;
      font-size: 14px;
    }

    .status-bar {
      background-color: #f5f5f5;
      padding: 8px 12px;
      border-radius: 4px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .status-icon {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-icon.loading {
      background-color: var(--warning-color);
      animation: pulse 1.5s infinite;
    }

    .status-icon.success {
      background-color: var(--success-color);
    }

    .status-icon.error {
      background-color: var(--error-color);
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 16px;
    }

    .tab {
      padding: 8px 16px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }

    .tab.active {
      border-bottom-color: var(--accent-color);
      color: var(--accent-color);
      font-weight: 500;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .visualization {
      margin-bottom: 24px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      overflow: hidden;
    }

    .visualization-header {
      padding: 12px;
      background-color: #f5f5f5;
      border-bottom: 1px solid var(--border-color);
      font-weight: 500;
    }

    .visualization-body {
      padding: 16px;
      min-height: 300px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: white;
    }

    .chart-container {
      width: 100%;
      height: 100%;
      min-height: 300px;
    }

    .spec-table {
      width: 100%;
      border-collapse: collapse;
    }

    .spec-table th, .spec-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    .spec-table th {
      background-color: #f5f5f5;
    }

    .spec-table tr:hover {
      background-color: var(--hover-color);
    }

    .sentiment-bar {
      height: 24px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      margin: 8px 0;
    }

    .sentiment-positive {
      height: 100%;
      background-color: #4CAF50;
      float: left;
      text-align: center;
      color: white;
      font-weight: bold;
      line-height: 24px;
      font-size: 12px;
    }

    .sentiment-negative {
      height: 100%;
      background-color: #D32F2F;
      float: right;
      text-align: center;
      color: white;
      font-weight: bold;
      line-height: 24px;
      font-size: 12px;
    }

    .pro-con-list {
      display: flex;
      justify-content: space-between;
      gap: 16px;
    }

    .pro-list, .con-list {
      flex: 1;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      padding: 12px;
    }

    .pro-list h3 {
      color: var(--success-color);
    }

    .con-list h3 {
      color: var(--error-color);
    }

    .pro-con-item {
      display: flex;
      justify-content: space-between;
      padding: 4px 0;
      border-bottom: 1px solid #eee;
    }

    .pro-con-item:last-child {
      border-bottom: none;
    }

    .pro-con-count {
      color: var(--light-text);
      font-weight: 500;
    }

    .empty-state {
      text-align: center;
      padding: 48px 0;
      color: var(--light-text);
    }

    .empty-state i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
    }

    .action-button {
      padding: 8px 12px;
      background-color: white;
      border: 1px solid var(--primary-color);
      color: var(--primary-color);
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .action-button.primary {
      background-color: var(--primary-color);
      color: white;
    }

    .action-button.danger {
      border-color: var(--error-color);
      color: var(--error-color);
    }

    .product-item {
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .product-item:hover {
      background-color: var(--hover-color);
    }

    .product-rating {
      font-weight: bold;
      min-width: 40px;
      text-align: center;
      margin-right: 12px;
    }

    .product-info {
      flex: 1;
    }

    .product-title {
      margin-bottom: 4px;
      font-weight: 500;
    }

    .product-meta {
      color: var(--light-text);
      font-size: 12px;
    }

    .product-price {
      font-weight: bold;
      margin-left: 8px;
    }

    .progress-container {
      margin-top: 16px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .progress-bar {
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
      margin: 8px 0;
    }

    .progress-fill {
      height: 100%;
      background-color: var(--primary-color);
      width: 0;
      transition: width 0.3s;
    }

    .progress-stats {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: var(--light-text);
    }

    .progress-time {
      font-size: 12px;
      margin-top: 4px;
      color: var(--light-text);
      text-align: right;
    }

    /* Chargement spinner */
    .loading-spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: var(--accent-color);
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Responsive */
    @media (max-width: 768px) {
      .pro-con-list {
        flex-direction: column;
      }
    }

    /* Styles pour le fallback en mode tableau simple */
    .data-table {
      width: 100%;
      border-collapse: collapse;
    }

    .data-table th, .data-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    .data-table th {
      background-color: #f5f5f5;
      position: sticky;
      top: 0;
    }

    .data-table tbody tr:hover {
      background-color: var(--hover-color);
    }

    .data-table-container {
      max-height: 400px;
      overflow-y: auto;
      margin-top: 16px;
    }

    .export-container {
      margin-top: 16px;
      text-align: right;
    }
  </style>
</head>
<body>
  <header>
    <div style="display: flex; align-items: center;">
      <h1>Amazon Review Insights - Analyse de marché</h1>
    </div>
    <span class="version">v0.2.1</span>
  </header>

  <div class="container">
    <div class="search-bar">
      <input type="text" class="search-input" id="keywordInput" placeholder="Entrez un mot-clé pour l'analyse de marché">
      <select id="localeSelect" class="search-input" style="flex: 0.2;">
        <option value="com">Amazon.com</option>
        <option value="fr">Amazon.fr</option>
      </select>
      <button id="searchButton" class="search-button">Analyser</button>
    </div>

    <div id="statusBar" class="status-bar" style="display: none;">
      <div>
        <span id="statusIcon" class="status-icon"></span>
        <span id="statusText">Chargement de l'analyse...</span>
      </div>
      <button id="cancelButton" style="display: none;">Annuler</button>
    </div>

    <div id="progressContainer" class="progress-container" style="display: none;">
      <div id="progressStage">Recherche des produits...</div>
      <div class="progress-bar">
        <div id="progressFill" class="progress-fill" style="width: 10%;"></div>
      </div>
      <div class="progress-stats">
        <div id="progressStats">0 / 0 produits analysés</div>
        <div id="progressPercentage">10%</div>
      </div>
      <div id="progressTime" class="progress-time">Temps écoulé: 0:00</div>
    </div>

    <div id="resultContainer" style="display: none;">
      <div class="tabs">
        <div class="tab active" data-tab="scatter">Prix × Notation</div>
        <div class="tab" data-tab="specs">Spécifications</div>
        <div class="tab" data-tab="sentiment">Sentiment</div>
        <div class="tab" data-tab="table">Tableau</div>
      </div>

      <!-- Tab: Prix × Notation (Scatter Plot) -->
      <div id="scatterTab" class="tab-content active">
        <div class="visualization">
          <div class="visualization-header">
            Prix (€) × Notation (★) - Taille des bulles: Nombre d'avis
          </div>
          <div class="visualization-body">
            <div id="scatterChart" class="chart-container"></div>
          </div>
        </div>
      </div>

      <!-- Tab: Spécifications (Heat Map) -->
      <div id="specsTab" class="tab-content">
        <div class="visualization">
          <div class="visualization-header">
            Tableau comparatif des spécifications
          </div>
          <div class="visualization-body">
            <div id="specTable" class="chart-container">
              <table class="spec-table" id="specTableContent">
                <thead>
                  <tr>
                    <th>Produit</th>
                    <th>Prix</th>
                    <th>Note</th>
                    <th>Avis</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab: Sentiment (Bar Chart) -->
      <div id="sentimentTab" class="tab-content">
        <div class="visualization">
          <div class="visualization-header">
            Sentiment par caractéristique
          </div>
          <div class="visualization-body">
            <div id="sentimentChart" class="chart-container"></div>
          </div>
        </div>

        <div class="pro-con-list">
          <div class="pro-list">
            <h3>Points positifs les plus mentionnés</h3>
            <div id="prosList"></div>
          </div>
          <div class="con-list">
            <h3>Points négatifs les plus mentionnés</h3>
            <div id="consList"></div>
          </div>
        </div>
      </div>

      <!-- Tab: Tableau (Data Table) -->
      <div id="tableTab" class="tab-content">
        <div class="data-table-container">
          <table class="data-table" id="fullDataTable">
            <thead>
              <tr>
                <th>ASIN</th>
                <th>Titre</th>
                <th>Prix</th>
                <th>Note</th>
                <th>Nb Avis</th>
                <th>Points Forts</th>
                <th>Points Faibles</th>
              </tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
        
        <div class="export-container">
          <button id="exportCsvButton" class="action-button">Exporter CSV</button>
        </div>
      </div>

      <div class="action-buttons">
        <button id="refreshButton" class="action-button">Actualiser</button>
        <button id="analyzeMoreButton" class="action-button primary">Analyser plus de produits</button>
      </div>
    </div>

    <div id="emptyState" class="empty-state">
      <i>📊</i>
      <h3>Analyse de marché Amazon</h3>
      <p>Entrez un mot-clé dans la barre de recherche ci-dessus pour commencer l'analyse</p>
    </div>
  </div>

  <script src="../common.js"></script>
  <script src="sidepanel.js"></script>
</body>
</html>