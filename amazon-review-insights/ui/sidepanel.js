/**
 * Amazon Review Insights - Panneau d'analyse de marché
 * Script gérant les visualisations de données et l'analyse de marché
 */

// Gestionnaire de l'interface
class MarketAnalysisUI {
  constructor() {
    this.currentKeyword = '';
    this.currentLocale = 'com';
    this.marketData = null;
    this.analysisInProgress = false;
    this.analysisStartTime = null;
    this.progressInterval = null;
    this.charts = {};
    
    // Initialiser le logger
    this.logger = console;
    this.initializeLogger();
    
    // Éléments DOM
    this.elements = {
      keywordInput: document.getElementById('keywordInput'),
      localeSelect: document.getElementById('localeSelect'),
      searchButton: document.getElementById('searchButton'),
      statusBar: document.getElementById('statusBar'),
      statusIcon: document.getElementById('statusIcon'),
      statusText: document.getElementById('statusText'),
      cancelButton: document.getElementById('cancelButton'),
      progressContainer: document.getElementById('progressContainer'),
      progressStage: document.getElementById('progressStage'),
      progressFill: document.getElementById('progressFill'),
      progressStats: document.getElementById('progressStats'),
      progressPercentage: document.getElementById('progressPercentage'),
      progressTime: document.getElementById('progressTime'),
      resultContainer: document.getElementById('resultContainer'),
      emptyState: document.getElementById('emptyState'),
      tabs: document.querySelectorAll('.tab'),
      tabContents: document.querySelectorAll('.tab-content'),
      refreshButton: document.getElementById('refreshButton'),
      analyzeMoreButton: document.getElementById('analyzeMoreButton'),
      exportCsvButton: document.getElementById('exportCsvButton'),
      
      // Charts containers
      scatterChart: document.getElementById('scatterChart'),
      specTable: document.getElementById('specTableContent'),
      sentimentChart: document.getElementById('sentimentChart'),
      prosList: document.getElementById('prosList'),
      consList: document.getElementById('consList'),
      fullDataTable: document.getElementById('fullDataTable')
    };
    
    // Attacher les écouteurs d'événements
    this.attachEventListeners();
    
    // Vérifier si des paramètres sont passés dans l'URL
    this.checkUrlParameters();
    
    // Récupérer la configuration
    this.loadConfig();
  }
  
  // Initialiser le logger depuis common.js
  async initializeLogger() {
    try {
      if (window.ariLogger) {
        this.logger = window.ariLogger.logger;
        this.logger.info('Logger initialisé dans le panneau d\'analyse de marché');
      } else {
        console.warn('Logger non disponible, utilisation de la console standard');
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du logger', error);
    }
  }
  
  // Attacher les écouteurs d'événements
  attachEventListeners() {
    // Recherche
    this.elements.searchButton.addEventListener('click', () => this.handleSearch());
    this.elements.keywordInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.handleSearch();
    });
    
    // Annulation
    this.elements.cancelButton.addEventListener('click', () => this.handleCancel());
    
    // Navigation par onglets
    this.elements.tabs.forEach(tab => {
      tab.addEventListener('click', () => this.switchTab(tab.getAttribute('data-tab')));
    });
    
    // Actions
    this.elements.refreshButton.addEventListener('click', () => this.handleRefresh());
    this.elements.analyzeMoreButton.addEventListener('click', () => this.handleAnalyzeMore());
    this.elements.exportCsvButton.addEventListener('click', () => this.exportToCsv());
  }
  
  // Vérifier les paramètres d'URL pour une recherche automatique
  checkUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const keyword = urlParams.get('keyword');
    const locale = urlParams.get('locale');
    
    if (keyword) {
      this.elements.keywordInput.value = keyword;
      if (locale && ['com', 'fr'].includes(locale)) {
        this.elements.localeSelect.value = locale;
      }
      // Démarrer l'analyse automatiquement après un délai
      setTimeout(() => this.handleSearch(), 500);
    }
  }
  
  // Charger la configuration
  async loadConfig() {
    try {
      // Obtenir la configuration du service worker
      const response = await chrome.runtime.sendMessage({ type: 'get_config' });
      
      if (response && response.config) {
        this.config = response.config;
        this.logger.debug('Configuration chargée', { config: this.config });
        
        // Initialiser la vue par défaut
        if (this.config.defaultVisualization) {
          this.switchTab(this.config.defaultVisualization);
        }
      } else {
        this.logger.warn('Impossible de charger la configuration');
      }
    } catch (error) {
      this.logger.error('Erreur lors du chargement de la configuration', { error });
    }
  }
  
  // Gérer la recherche
  async handleSearch() {
    const keyword = this.elements.keywordInput.value.trim();
    const locale = this.elements.localeSelect.value;
    
    if (!keyword) {
      this.showStatus('error', 'Veuillez entrer un mot-clé');
      return;
    }
    
    this.currentKeyword = keyword;
    this.currentLocale = locale;
    
    // Mise à jour de l'URL pour permettre le partage/refresh
    const url = new URL(window.location.href);
    url.searchParams.set('keyword', keyword);
    url.searchParams.set('locale', locale);
    window.history.replaceState({}, '', url);
    
    // Réinitialiser l'UI
    this.resetUI();
    
    // Démarrer l'analyse
    await this.startMarketAnalysis(keyword, locale);
  }
  
  // Démarrer l'analyse de marché
  async startMarketAnalysis(keyword, locale, force = false) {
    try {
      this.showStatus('loading', `Analyse en cours pour "${keyword}" sur Amazon.${locale}`);
      this.elements.cancelButton.style.display = 'block';
      this.analysisInProgress = true;
      this.analysisStartTime = Date.now();
      
      // Démarrer le timer de progression
      this.startProgressTimer();
      
      // Envoyer la requête d'analyse au service worker
      const response = await chrome.runtime.sendMessage({
        type: 'market_analyze',
        keyword,
        locale,
        force
      });
      
      // Traiter la réponse
      if (response.error) {
        this.showStatus('error', `Erreur: ${response.error}`);
        this.stopProgressTimer();
        this.analysisInProgress = false;
        return;
      }
      
      // Si déjà en cache, afficher directement
      if (response.cached) {
        this.marketData = response.data;
        this.showStatus('success', `Données chargées depuis le cache (${response.age} heures)`);
        this.displayResults();
        this.stopProgressTimer();
        this.analysisInProgress = false;
        return;
      }
      
      // Si l'analyse a été lancée, commencer à surveiller le progrès
      if (response.started) {
        this.monitorAnalysisProgress(keyword, locale);
      } else if (response.inProgress) {
        this.showStatus('loading', 'Analyse déjà en cours, surveillance du progrès...');
        this.monitorAnalysisProgress(keyword, locale);
      }
    } catch (error) {
      this.logger.error('Erreur lors du démarrage de l\'analyse', { error });
      this.showStatus('error', `Erreur: ${error.message}`);
      this.stopProgressTimer();
      this.analysisInProgress = false;
    }
  }
  
  // Surveiller le progrès de l'analyse
  async monitorAnalysisProgress(keyword, locale) {
    let checkCount = 0;
    let lastProgress = null;
    
    const checkProgress = async () => {
      try {
        const response = await chrome.runtime.sendMessage({
          type: 'market_get_data',
          keyword,
          locale
        });
        
        // Si l'analyse est terminée et les données sont disponibles
        if (response.cached && !response.inProgress) {
          this.marketData = response.data;
          this.showStatus('success', 'Analyse terminée avec succès');
          this.displayResults();
          this.stopProgressTimer();
          this.analysisInProgress = false;
          return;
        }
        
        // Si l'analyse est en cours, mettre à jour l'UI
        if (response.inProgress && response.progress) {
          const progress = response.progress;
          lastProgress = progress;
          
          // Mise à jour de l'indicateur de progrès
          this.updateProgress(progress);
          
          // Continuer à vérifier le progrès
          setTimeout(checkProgress, 2000);
        } else {
          // Pas d'analyse en cours mais pas de données non plus
          if (checkCount < 10) {
            checkCount++;
            setTimeout(checkProgress, 2000);
          } else {
            this.showStatus('error', 'L\'analyse a été interrompue ou a échoué');
            this.stopProgressTimer();
            this.analysisInProgress = false;
          }
        }
      } catch (error) {
        this.logger.error('Erreur lors de la surveillance du progrès', { error });
        
        // Réessayer quelques fois en cas d'erreur temporaire
        if (checkCount < 5) {
          checkCount++;
          setTimeout(checkProgress, 3000);
        } else {
          this.showStatus('error', `Erreur: ${error.message}`);
          this.stopProgressTimer();
          this.analysisInProgress = false;
        }
      }
    };
    
    // Première vérification
    checkProgress();
  }
  
  // Mettre à jour l'indicateur de progrès
  updateProgress(progress) {
    // Statut
    this.elements.statusText.textContent = `Analyse en cours: ${progress.status}`;
    
    // Étape
    let stageText = 'Analyse en cours...';
    if (progress.status === 'fetching_products') {
      stageText = `Recherche des produits (page ${progress.currentPage})`;
    } else if (progress.status === 'analyzing_products') {
      stageText = `Analyse des produits`;
    } else if (progress.status === 'blocked') {
      stageText = `Amazon bloque les requêtes, arrêt...`;
    } else if (progress.status === 'error') {
      stageText = `Erreur: ${progress.error}`;
    }
    this.elements.progressStage.textContent = stageText;
    
    // Statistiques
    if (progress.productsFound > 0) {
      const analyzed = progress.productsAnalyzed || 0;
      const percentage = analyzed > 0 
        ? Math.round((analyzed / progress.productsFound) * 100)
        : 0;
      
      this.elements.progressStats.textContent = `${analyzed} / ${progress.productsFound} produits analysés`;
      this.elements.progressPercentage.textContent = `${percentage}%`;
      this.elements.progressFill.style.width = `${percentage}%`;
    }
    
    // Afficher le conteneur de progression
    this.elements.progressContainer.style.display = 'block';
  }
  
  // Démarrer le timer pour afficher le temps écoulé
  startProgressTimer() {
    this.stopProgressTimer();
    const startTime = Date.now();
    
    this.progressInterval = setInterval(() => {
      const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
      const minutes = Math.floor(elapsedSeconds / 60);
      const seconds = elapsedSeconds % 60;
      
      this.elements.progressTime.textContent = 
        `Temps écoulé: ${minutes}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
  }
  
  // Arrêter le timer de progression
  stopProgressTimer() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  }
  
  // Afficher les résultats
  displayResults() {
    // Cacher l'état vide et afficher les résultats
    this.elements.emptyState.style.display = 'none';
    this.elements.resultContainer.style.display = 'block';
    
    // Générer les visualisations
    this.renderScatterPlot();
    this.renderSpecTable();
    this.renderSentimentChart();
    this.renderDataTable();
    
    // Afficher le nombre de produits analysés
    const productsCount = this.marketData?.items?.length || 0;
    this.elements.statusText.textContent = `${productsCount} produits analysés pour "${this.currentKeyword}"`;
  }
  
  // Générer le graphique à bulles prix × note
  renderScatterPlot() {
    try {
      const items = this.marketData?.items || [];
      
      if (items.length === 0) {
        this.elements.scatterChart.innerHTML = '<div class="empty-state">Aucune donnée disponible</div>';
        return;
      }
      
      // Préparer les données pour Recharts
      const data = items
        .filter(item => item.price && item.rating && item.reviewCount)
        .map(item => ({
          asin: item.asin,
          title: item.title,
          price: item.price,
          rating: item.rating,
          reviewCount: item.reviewCount || 0,
        }));
      
      // Utiliser Recharts pour créer le graphique
      const { ScatterChart, Scatter, XAxis, YAxis, ZAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } = Recharts;
      
      // Nettoyer le conteneur
      while (this.elements.scatterChart.firstChild) {
        this.elements.scatterChart.removeChild(this.elements.scatterChart.firstChild);
      }
      
      try {
        // Créer le graphique React/Recharts
        const scatterChart = React.createElement(
          ResponsiveContainer,
          { width: '100%', height: 400 },
          React.createElement(
            ScatterChart,
            { margin: { top: 20, right: 20, bottom: 20, left: 20 } },
            React.createElement(CartesianGrid),
            React.createElement(XAxis, { type: 'number', dataKey: 'price', name: 'Prix', unit: '€' }),
            React.createElement(YAxis, { type: 'number', dataKey: 'rating', name: 'Note', domain: [0, 5] }),
            React.createElement(ZAxis, { type: 'number', dataKey: 'reviewCount', range: [50, 500], name: 'Nombre d\'avis' }),
            React.createElement(Tooltip, { 
              cursor: { strokeDasharray: '3 3' },
              formatter: (value, name) => {
                if (name === 'price') return [`${value} €`, 'Prix'];
                if (name === 'rating') return [value, 'Note'];
                if (name === 'reviewCount') return [value, 'Nombre d\'avis'];
                return [value, name];
              },
              labelFormatter: (index) => data[index]?.title || ''
            }),
            React.createElement(Legend),
            React.createElement(Scatter, { 
              name: 'Produits', 
              data: data, 
              fill: '#FF9900'
            })
          )
        );
        
        ReactDOM.render(scatterChart, this.elements.scatterChart);
        
      } catch (reactError) {
        // Fallback si Recharts échoue
        this.logger.error('Erreur lors du rendu du graphique', { error: reactError });
        this.renderScatterPlotFallback(data);
      }
    } catch (error) {
      this.logger.error('Erreur lors de la génération du graphique à bulles', { error });
      this.elements.scatterChart.innerHTML = 
        `<div class="empty-state">Erreur lors de la génération du graphique</div>`;
    }
  }
  
  // Rendu de secours pour le graphique à bulles (simple tableau)
  renderScatterPlotFallback(data) {
    const table = document.createElement('table');
    table.className = 'data-table';
    
    // En-tête
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    ['Produit', 'Prix', 'Note', 'Nb Avis'].forEach(header => {
      const th = document.createElement('th');
      th.textContent = header;
      headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Corps du tableau
    const tbody = document.createElement('tbody');
    
    data.forEach(item => {
      const row = document.createElement('tr');
      
      const titleCell = document.createElement('td');
      titleCell.textContent = item.title;
      row.appendChild(titleCell);
      
      const priceCell = document.createElement('td');
      priceCell.textContent = `${item.price} €`;
      row.appendChild(priceCell);
      
      const ratingCell = document.createElement('td');
      ratingCell.textContent = item.rating;
      row.appendChild(ratingCell);
      
      const reviewsCell = document.createElement('td');
      reviewsCell.textContent = item.reviewCount;
      row.appendChild(reviewsCell);
      
      tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    
    // Nettoyer et insérer
    this.elements.scatterChart.innerHTML = '';
    this.elements.scatterChart.appendChild(table);
  }
  
  // Générer le tableau comparatif des spécifications
  renderSpecTable() {
    try {
      const items = this.marketData?.items || [];
      
      if (items.length === 0) {
        this.elements.specTable.innerHTML = '<div class="empty-state">Aucune donnée disponible</div>';
        return;
      }
      
      // Collecter toutes les spécifications uniques
      const specKeys = new Set();
      items.forEach(item => {
        if (item.specs) {
          Object.keys(item.specs).forEach(key => specKeys.add(key));
        }
      });
      
      // Si aucune spécification trouvée
      if (specKeys.size === 0) {
        this.elements.specTable.innerHTML = 
          '<div class="empty-state">Aucune spécification trouvée pour ces produits</div>';
        return;
      }
      
      // Construire l'en-tête du tableau
      const thead = document.createElement('thead');
      const headerRow = document.createElement('tr');
      
      // Colonne pour le nom du produit
      const productHeader = document.createElement('th');
      productHeader.textContent = 'Produit';
      headerRow.appendChild(productHeader);
      
      // Colonnes pour les spécifications
      Array.from(specKeys).sort().forEach(specKey => {
        const th = document.createElement('th');
        th.textContent = specKey;
        headerRow.appendChild(th);
      });
      
      thead.appendChild(headerRow);
      
      // Construire le corps du tableau
      const tbody = document.createElement('tbody');
      
      items.forEach(item => {
        const row = document.createElement('tr');
        
        // Cellule pour le nom du produit
        const productCell = document.createElement('td');
        productCell.textContent = item.title;
        row.appendChild(productCell);
        
        // Cellules pour les spécifications
        Array.from(specKeys).sort().forEach(specKey => {
          const td = document.createElement('td');
          if (item.specs && item.specs[specKey]) {
            td.textContent = item.specs[specKey];
          } else {
            td.textContent = '-';
            td.style.color = '#ccc';
          }
          row.appendChild(td);
        });
        
        tbody.appendChild(row);
      });
      
      // Nettoyer et insérer
      const table = document.createElement('table');
      table.className = 'spec-table';
      table.appendChild(thead);
      table.appendChild(tbody);
      
      this.elements.specTable.innerHTML = '';
      this.elements.specTable.appendChild(table);
      
    } catch (error) {
      this.logger.error('Erreur lors de la génération du tableau de spécifications', { error });
      this.elements.specTable.innerHTML = 
        `<div class="empty-state">Erreur lors de la génération du tableau</div>`;
    }
  }
  
  // Générer le graphique de sentiment
  renderSentimentChart() {
    try {
      const stats = this.marketData?.stats || {};
      const mostMentionedPros = stats.mostMentionedPros || [];
      const mostMentionedCons = stats.mostMentionedCons || [];
      
      // Remplir les listes de points positifs/négatifs
      this.elements.prosList.innerHTML = '';
      mostMentionedPros.forEach(pro => {
        const item = document.createElement('div');
        item.className = 'pro-con-item';
        item.innerHTML = `
          <span>${pro.text}</span>
          <span class="pro-con-count">${pro.count}</span>
        `;
        this.elements.prosList.appendChild(item);
      });
      
      this.elements.consList.innerHTML = '';
      mostMentionedCons.forEach(con => {
        const item = document.createElement('div');
        item.className = 'pro-con-item';
        item.innerHTML = `
          <span>${con.text}</span>
          <span class="pro-con-count">${con.count}</span>
        `;
        this.elements.consList.appendChild(item);
      });
      
      // Si aucune donnée de sentiment
      if (mostMentionedPros.length === 0 && mostMentionedCons.length === 0) {
        this.elements.sentimentChart.innerHTML = 
          '<div class="empty-state">Aucune donnée de sentiment disponible</div>';
        return;
      }
      
      // Créer les graphiques de sentiment
      try {
        // Nettoyer le conteneur
        this.elements.sentimentChart.innerHTML = '';
        
        // Combiner pros et cons pour le graphique
        const sentimentData = [];
        
        // Trouver les thématiques communes
        const topics = new Set();
        
        // Extraire les thématiques des pros/cons
        const extractTopics = (text) => {
          // Diviser en mots
          const words = text.toLowerCase().split(/\s+/);
          // Filtrer les mots courts et stopwords
          const significantWords = words.filter(w => 
            w.length > 3 && 
            !['avec', 'pour', 'dans', 'cette', 'sont', 'mais', 'très', 'plus', 'bien'].includes(w)
          );
          return significantWords.slice(0, 2).join(' ');
        };
        
        // Créer une map des thématiques
        const topicSentiments = new Map();
        
        // Traiter les points positifs
        mostMentionedPros.forEach(pro => {
          const topic = extractTopics(pro.text);
          if (topic) {
            const current = topicSentiments.get(topic) || { positive: 0, negative: 0 };
            current.positive += pro.count;
            topicSentiments.set(topic, current);
            topics.add(topic);
          }
        });
        
        // Traiter les points négatifs
        mostMentionedCons.forEach(con => {
          const topic = extractTopics(con.text);
          if (topic) {
            const current = topicSentiments.get(topic) || { positive: 0, negative: 0 };
            current.negative += con.count;
            topicSentiments.set(topic, current);
            topics.add(topic);
          }
        });
        
        // Créer le graphique pour chaque thématique
        topics.forEach(topic => {
          const sentiment = topicSentiments.get(topic) || { positive: 0, negative: 0 };
          const total = sentiment.positive + sentiment.negative;
          if (total === 0) return;
          
          const positivePercentage = Math.round((sentiment.positive / total) * 100);
          const negativePercentage = 100 - positivePercentage;
          
          const sentimentBar = document.createElement('div');
          sentimentBar.className = 'sentiment-container';
          sentimentBar.innerHTML = `
            <div style="margin-bottom: 4px;">${topic}</div>
            <div class="sentiment-bar">
              <div class="sentiment-positive" style="width: ${positivePercentage}%">${positivePercentage}%</div>
              <div class="sentiment-negative" style="width: ${negativePercentage}%">${negativePercentage}%</div>
            </div>
          `;
          
          this.elements.sentimentChart.appendChild(sentimentBar);
        });
        
      } catch (chartError) {
        this.logger.error('Erreur lors du rendu du graphique de sentiment', { error: chartError });
        this.elements.sentimentChart.innerHTML = 
          '<div class="empty-state">Erreur lors de la génération du graphique</div>';
      }
      
    } catch (error) {
      this.logger.error('Erreur lors de la génération du graphique de sentiment', { error });
      this.elements.sentimentChart.innerHTML = 
        `<div class="empty-state">Erreur lors de la génération du graphique</div>`;
    }
  }
  
  // Générer le tableau complet des données
  renderDataTable() {
    try {
      const items = this.marketData?.items || [];
      
      if (items.length === 0) {
        this.elements.fullDataTable.innerHTML = '<div class="empty-state">Aucune donnée disponible</div>';
        return;
      }
      
      // Vider le tableau existant
      const tbody = this.elements.fullDataTable.querySelector('tbody');
      tbody.innerHTML = '';
      
      // Remplir le tableau
      items.forEach(item => {
        const row = document.createElement('tr');
        
        // ASIN
        const asinCell = document.createElement('td');
        asinCell.textContent = item.asin;
        row.appendChild(asinCell);
        
        // Titre
        const titleCell = document.createElement('td');
        titleCell.textContent = item.title;
        row.appendChild(titleCell);
        
        // Prix
        const priceCell = document.createElement('td');
        priceCell.textContent = item.price ? `${item.price} ${item.currency || '€'}` : '-';
        row.appendChild(priceCell);
        
        // Note
        const ratingCell = document.createElement('td');
        ratingCell.textContent = item.rating || '-';
        row.appendChild(ratingCell);
        
        // Nombre d'avis
        const reviewsCell = document.createElement('td');
        reviewsCell.textContent = item.reviewCount || '0';
        row.appendChild(reviewsCell);
        
        // Points forts
        const prosCell = document.createElement('td');
        if (item.analysis && item.analysis.pros && item.analysis.pros.length > 0) {
          const prosList = document.createElement('ul');
          item.analysis.pros.forEach(pro => {
            const li = document.createElement('li');
            li.textContent = pro.text;
            prosList.appendChild(li);
          });
          prosCell.appendChild(prosList);
        } else {
          prosCell.textContent = 'Non disponible';
        }
        row.appendChild(prosCell);
        
        // Points faibles
        const consCell = document.createElement('td');
        if (item.analysis && item.analysis.cons && item.analysis.cons.length > 0) {
          const consList = document.createElement('ul');
          item.analysis.cons.forEach(con => {
            const li = document.createElement('li');
            li.textContent = con.text;
            consList.appendChild(li);
          });
          consCell.appendChild(consList);
        } else {
          consCell.textContent = 'Non disponible';
        }
        row.appendChild(consCell);
        
        tbody.appendChild(row);
      });
      
    } catch (error) {
      this.logger.error('Erreur lors de la génération du tableau de données', { error });
      this.elements.fullDataTable.querySelector('tbody').innerHTML = 
        `<tr><td colspan="7">Erreur lors de la génération du tableau</td></tr>`;
    }
  }
  
  // Exporter les données au format CSV
  exportToCsv() {
    try {
      const items = this.marketData?.items || [];
      
      if (items.length === 0) {
        this.showStatus('error', 'Aucune donnée à exporter');
        return;
      }
      
      // Préparer l'en-tête
      const headers = [
        'ASIN', 
        'Titre', 
        'Prix', 
        'Devise', 
        'Note', 
        'Nb Avis',
        'Points Forts',
        'Points Faibles',
        'URL'
      ];
      
      // Préparer les lignes de données
      const rows = items.map(item => {
        // Fonction pour préparer les pros/cons en texte
        const formatPoints = (points) => {
          if (!points || !Array.isArray(points) || points.length === 0) return '';
          return points.map(p => p.text).join('; ');
        };
        
        return [
          item.asin || '',
          item.title || '',
          item.price || '',
          item.currency || '',
          item.rating || '',
          item.reviewCount || '',
          formatPoints(item.analysis?.pros),
          formatPoints(item.analysis?.cons),
          `https://www.amazon.${this.currentLocale}/dp/${item.asin}`
        ];
      });
      
      // Fonction pour échapper les valeurs CSV
      const escapeCsv = (value) => {
        if (value === null || value === undefined) return '';
        return `"${String(value).replace(/"/g, '""')}"`;
      };
      
      // Générer le contenu CSV
      const csvContent = [
        headers.map(escapeCsv).join(','),
        ...rows.map(row => row.map(escapeCsv).join(','))
      ].join('\n');
      
      // Créer un Blob et le télécharger
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `amazon-market-${this.currentKeyword}-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      this.showStatus('success', 'Données exportées au format CSV');
      
    } catch (error) {
      this.logger.error('Erreur lors de l\'exportation CSV', { error });
      this.showStatus('error', `Erreur lors de l'exportation: ${error.message}`);
    }
  }
  
  // Changer d'onglet
  switchTab(tabName) {
    // Désactiver tous les onglets
    this.elements.tabs.forEach(tab => {
      tab.classList.remove('active');
    });
    
    this.elements.tabContents.forEach(content => {
      content.classList.remove('active');
    });
    
    // Activer l'onglet sélectionné
    const selectedTab = document.querySelector(`.tab[data-tab="${tabName}"]`);
    const selectedContent = document.getElementById(`${tabName}Tab`);
    
    if (selectedTab && selectedContent) {
      selectedTab.classList.add('active');
      selectedContent.classList.add('active');
    }
  }
  
  // Gérer l'actualisation des données
  async handleRefresh() {
    if (this.analysisInProgress) {
      this.showStatus('error', 'Une analyse est déjà en cours');
      return;
    }
    
    if (!this.currentKeyword) {
      this.showStatus('error', 'Aucune analyse précédente à actualiser');
      return;
    }
    
    // Forcer une nouvelle analyse
    await this.startMarketAnalysis(this.currentKeyword, this.currentLocale, true);
  }
  
  // Gérer l'analyse de plus de produits
  async handleAnalyzeMore() {
    // À implémenter pour les analyses futures
    this.showStatus('error', 'Cette fonctionnalité sera disponible dans une prochaine version');
  }
  
  // Gérer l'annulation de l'analyse
  async handleCancel() {
    if (!this.analysisInProgress) return;
    
    try {
      // Envoyer une requête d'annulation au service worker
      const response = await chrome.runtime.sendMessage({
        type: 'market_cancel_analysis',
        keyword: this.currentKeyword,
        locale: this.currentLocale
      });
      
      if (response.success) {
        this.showStatus('error', 'Analyse annulée');
      } else {
        this.showStatus('error', response.error || 'Impossible d\'annuler l\'analyse');
      }
      
      this.stopProgressTimer();
      this.analysisInProgress = false;
    } catch (error) {
      this.logger.error('Erreur lors de l\'annulation', { error });
      this.showStatus('error', `Erreur: ${error.message}`);
    }
  }
  
  // Afficher un message de statut
  showStatus(type, text) {
    this.elements.statusBar.style.display = 'flex';
    this.elements.statusText.textContent = text;
    
    // Réinitialiser les classes
    this.elements.statusIcon.classList.remove('loading', 'success', 'error');
    
    // Ajouter la classe appropriée
    this.elements.statusIcon.classList.add(type);
    
    // Masquer le bouton d'annulation pour les statuts finaux
    if (type !== 'loading') {
      this.elements.cancelButton.style.display = 'none';
    }
  }
  
  // Réinitialiser l'interface utilisateur
  resetUI() {
    // Masquer les résultats et montrer l'état vide
    this.elements.resultContainer.style.display = 'none';
    this.elements.emptyState.style.display = 'block';
    
    // Réinitialiser les graphiques
    this.elements.scatterChart.innerHTML = '';
    this.elements.specTable.innerHTML = '';
    this.elements.sentimentChart.innerHTML = '';
    this.elements.prosList.innerHTML = '';
    this.elements.consList.innerHTML = '';
    this.elements.fullDataTable.querySelector('tbody').innerHTML = '';
    
    // Masquer la progression
    this.elements.progressContainer.style.display = 'none';
  }
}

// Initialiser l'application
document.addEventListener('DOMContentLoaded', () => {
  window.marketAnalysisUI = new MarketAnalysisUI();
});