/**
 * Amazon Review Insights - Configuration et gestion du stockage
 */

// Configuration par défaut
const DEFAULT_CONFIG = {
  autoAnalysisEnabled: false,
  cacheDurationOption: '1_semaine',
  maxParallelAnalyses: 5,
  logLevel: 3,
  logToConsole: true, 
  logToStorage: true,
  maxStoredLogs: 1000,
  // Nouvelles options pour l'analyse de marché
  marketReviewsPerProduct: 300,
  marketParallelFetches: 3,
  marketAutoAnalysis: false,
  defaultVisualization: 'scatter'
};

// Configuration de durée de cache
const CACHE_DURATION = {
  '1_jour': 24 * 60 * 60 * 1000,
  '1_semaine': 7 * 24 * 60 * 60 * 1000,
  '1_mois': 30 * 24 * 60 * 60 * 1000,
  '6_mois': 180 * 24 * 60 * 60 * 1000,
  'toujours': Number.MAX_SAFE_INTEGER
};

// Gestionnaire de configuration
self.ariConfig = {
  // Récupérer la configuration
  async getConfig() {
    try {
      const { config = {} } = await chrome.storage.local.get('config');
      // Fusionner avec la config par défaut
      return { ...DEFAULT_CONFIG, ...config };
    } catch (error) {
      console.error('Erreur lors de la récupération de la config', error);
      return DEFAULT_CONFIG;
    }
  },
  
  // Sauvegarder la configuration
  async saveConfig(newConfig) {
    try {
      const config = { ...DEFAULT_CONFIG, ...newConfig };
      await chrome.storage.local.set({ config });
      return config;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la config', error);
      throw error;
    }
  },
  
  // Vérifier si une analyse est expirée
  async isAnalysisExpired(timestamp) {
    const config = await this.getConfig();
    const duration = CACHE_DURATION[config.cacheDurationOption] || CACHE_DURATION['1_semaine'];
    return Date.now() - timestamp > duration;
  },
  
  // Créer une clé pour les données de marché
  createMarketKey(keyword, locale = 'com') {
    return `market:${keyword}|${locale}`;
  },
  
  // Créer une clé pour les données de revue
  createReviewKey(hash) {
    return `review:${hash}`;
  },
  
  // Récupérer les données de marché
  async getMarketData(keyword, locale = 'com') {
    try {
      const key = this.createMarketKey(keyword, locale);
      const result = await chrome.storage.local.get(key);
      
      if (!result[key]) {
        return { exists: false, data: null };
      }
      
      const data = result[key];
      const ageInHours = Math.round((Date.now() - data.ts) / (60 * 60 * 1000));
      const expired = await this.isAnalysisExpired(data.ts);
      
      return {
        exists: true,
        data,
        age: ageInHours,
        expired
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des données de marché', error);
      return { exists: false, error: error.message };
    }
  },
  
  // Sauvegarder les données de marché
  async saveMarketData(keyword, locale = 'com', data) {
    try {
      const key = this.createMarketKey(keyword, locale);
      
      // Ajouter horodatage si absent
      if (!data.ts) {
        data.ts = Date.now();
      }
      
      // Sauvegarder les données
      await chrome.storage.local.set({ [key]: data });
      
      // Nettoyer le stockage pour éviter de dépasser la limite de 5 Mo
      await this.pruneStorage();
      
      return true;
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des données de marché', error);
      throw error;
    }
  },
  
  // Nettoyer le stockage pour éviter de dépasser la limite
  async pruneStorage() {
    try {
      const config = await this.getConfig();
      const duration = CACHE_DURATION[config.cacheDurationOption] || CACHE_DURATION['1_semaine'];
      
      // Récupérer toutes les données du stockage local
      const storage = await chrome.storage.local.get(null);
      
      // Identifier les clés à supprimer
      const keysToRemove = [];
      const now = Date.now();
      
      // Séparer les entrées par type
      const marketEntries = [];
      const reviewEntries = [];
      
      // Analyser toutes les clés
      for (const [key, value] of Object.entries(storage)) {
        // Ignorer les clés spéciales
        if (key === 'config' || key === 'logs' || key === 'key') continue;
        
        if (key.startsWith('market:')) {
          marketEntries.push({ key, data: value, age: now - value.ts });
        } else if (key.startsWith('review:')) {
          reviewEntries.push({ key, data: value, age: now - value.ts });
        }
      }
      
      // Trier par âge (du plus ancien au plus récent)
      marketEntries.sort((a, b) => b.age - a.age);
      reviewEntries.sort((a, b) => b.age - a.age);
      
      // Supprimer les entrées de marché expirées
      for (const entry of marketEntries) {
        if (entry.age > duration) {
          keysToRemove.push(entry.key);
        }
      }
      
      // Supprimer les entrées de revue qui n'ont pas été consultées depuis longtemps
      const reviewTTL = Math.max(duration, 30 * 24 * 60 * 60 * 1000); // minimum 30 jours
      for (const entry of reviewEntries) {
        if (entry.age > reviewTTL) {
          keysToRemove.push(entry.key);
        }
      }
      
      // Supprimer les entrées identifiées
      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`${keysToRemove.length} entrées supprimées du stockage`);
      }
      
      // Vérifier la taille du stockage
      const storageInfo = await this.getStorageInfo();
      if (storageInfo.bytesInUse > 4 * 1024 * 1024) { // Si on dépasse 4 Mo
        // Mode d'urgence : supprimer plus d'entrées
        console.warn(`Stockage critique: ${(storageInfo.bytesInUse / (1024 * 1024)).toFixed(2)} Mo utilisés`);
        
        // Supprimer les entrées les plus anciennes jusqu'à être sous 3.5 Mo
        const remainingEntries = [...marketEntries, ...reviewEntries]
          .filter(entry => !keysToRemove.includes(entry.key))
          .sort((a, b) => b.age - a.age);
        
        const additionalKeysToRemove = [];
        for (const entry of remainingEntries) {
          additionalKeysToRemove.push(entry.key);
          
          // Vérifier si on a suffisamment libéré d'espace
          if (additionalKeysToRemove.length >= 10) {
            // On libère par lots de 10
            await chrome.storage.local.remove(additionalKeysToRemove);
            console.warn(`${additionalKeysToRemove.length} entrées supplémentaires supprimées du stockage (urgence)`);
            
            // Vérifier à nouveau la taille
            const updatedInfo = await this.getStorageInfo();
            if (updatedInfo.bytesInUse < 3.5 * 1024 * 1024) {
              break; // On a suffisamment libéré d'espace
            }
            
            // Réinitialiser pour le prochain lot
            additionalKeysToRemove.length = 0;
          }
        }
        
        // Supprimer les entrées restantes
        if (additionalKeysToRemove.length > 0) {
          await chrome.storage.local.remove(additionalKeysToRemove);
          console.warn(`${additionalKeysToRemove.length} entrées supplémentaires supprimées du stockage (urgence)`);
        }
      }
    } catch (error) {
      console.error('Erreur lors du nettoyage du stockage', error);
    }
  },
  
  // Obtenir des informations sur le stockage
  async getStorageInfo() {
    return new Promise((resolve) => {
      chrome.storage.local.getBytesInUse(null, (bytesInUse) => {
        resolve({
          bytesInUse,
          bytesInUseMB: (bytesInUse / (1024 * 1024)).toFixed(2),
          quota: chrome.storage.local.QUOTA_BYTES,
          quotaMB: (chrome.storage.local.QUOTA_BYTES / (1024 * 1024)).toFixed(2)
        });
      });
    });
  },
  
  // Génération de hash SHA-256 pour les reviews
  async generateSHA256(text) {
    const msgBuffer = new TextEncoder().encode(text);
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }
};
