// Importer le système de logs
importScripts('common.js');
importScripts('config.js');

const API_KEY = '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';
// La variable CACHE_DURATION est maintenant définie dans la configuration

// Initialiser le logger - utiliser directement self.ariLogger pour éviter les conflits
// Ne pas redéclarer des fonctions déjà importées par importScripts
// Référencer self.ariLogger.logger pour les appels de log
const loggerRef = self.ariLogger?.logger || console;
const measureRef = self.ariLogger?.measure || (async (label, fn) => fn());

// En début de fichier, ajouter:
console.log('Service worker version 0.3.0 (Mode Automatique) chargé le', new Date().toISOString());

// Utilitaires
const delay = ms => new Promise(r => setTimeout(r, ms));
const randomDelay = () => delay(600 + Math.random() * 400);

// Extraction des avis avec regex
function extractReviews(html) {
  loggerRef.debug('Extraction des avis avec regex', { htmlSize: html.length });
  
  const reviewRegex = /class="review-text-content"[^>]*>[\s\n]*<span[^>]*>([^<]+)<\/span>/g;
  const reviews = [];
  let match;
  while ((match = reviewRegex.exec(html)) !== null) {
    const text = match[1].trim();
    if (text.length > 10) {
      reviews.push(text);
    }
  }
  
  loggerRef.debug('Résultat extraction regex', { count: reviews.length });
  return reviews;
}

// Détection de la dernière page
function hasLastPageMarker(html) {
  const result = html.includes('class="a-disabled a-last"');
  loggerRef.debug('Vérification marqueur dernière page', { found: result });
  return result;
}

// Structure pour stocker les informations sur les reviews en cours de téléchargement
const reviewsInProgress = new Map();

// NOUVELLE STRUCTURE POUR L'ANALYSE DE MARCHÉ
// Map pour suivre les analyses de marché en cours
const marketAnalysisInProgress = new Map();

// Gestionnaire de messages
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  loggerRef.debug('Message reçu', { type: message.type, sender: sender.id });
  
  switch (message.type) {
    case 'fetch':
      handleFetch(message, sendResponse);
      return true;
    case 'fetch_more':
      handleFetchMore(message, sendResponse);
      return true;
    case 'get_cache':
      handleGetCache(message.asin, sendResponse);
      return true;
    // NOUVEAUX GESTIONNAIRES DE MESSAGES POUR L'ANALYSE DE MARCHÉ
    case 'market_analyze':
      handleMarketAnalyze(message, sendResponse);
      return true;
    case 'market_get_data':
      handleMarketGetData(message, sendResponse);
      return true;
    case 'market_fetch_products':
      handleMarketFetchProducts(message, sendResponse);
      return true;
    case 'market_cancel_analysis':
      handleMarketCancelAnalysis(message, sendResponse);
      return true;
    case 'get_logs':
      // Gestionnaire pour récupérer les logs
      self.ariLogger.getLogs().then(logs => {
        loggerRef.debug('Envoi des logs', { count: logs.entries.length });
        sendResponse({ entries: logs.entries });
      }).catch(error => {
        loggerRef.error('Erreur lors de la récupération des logs', { error: error.message });
        sendResponse({ entries: [], error: error.message });
      });
      return true;
    case 'clear_logs':
      // Gestionnaire pour effacer les logs
      self.ariLogger.clearLogs().then(() => {
        loggerRef.info('Logs effacés');
        sendResponse({ success: true });
      }).catch(error => {
        loggerRef.error('Erreur lors de l\'effacement des logs', { error: error.message });
        sendResponse({ success: false, error: error.message });
      });
      return true;
    case 'ping':
      // Simple gestionnaire pour vérifier que le service worker répond
      loggerRef.debug('Ping reçu', { from: sender.id });
      sendResponse({ status: 'ok', timestamp: Date.now() });
      return true;
    case 'get_config':
      // Gestionnaire pour fournir la configuration aux scripts de contenu
      self.ariConfig.getConfig().then(config => {
        loggerRef.debug('Envoi de la configuration', { config });
        sendResponse({ config });
      }).catch(error => {
        loggerRef.error('Erreur lors de la récupération de la config', { error: error.message });
        sendResponse({ error: error.message });
      });
      return true;
    case 'save_config':
      // Sauvegarder la config
      self.ariConfig.saveConfig(message.config).then(config => {
        loggerRef.debug('Configuration sauvegardée', { config });
        sendResponse({ success: true, config });
      }).catch(error => {
        loggerRef.error('Erreur lors de la sauvegarde de la config', { error: error.message });
        sendResponse({ success: false, error: error.message });
      });
      return true;
  }
});

// Vérification du cache
async function handleGetCache(asin, sendResponse) {
  loggerRef.debug('Vérification du cache', { asin });
  
  try {
    // Récupérer la configuration pour avoir la durée de cache
    const config = await self.ariConfig.getConfig();
    const cacheDuration = config.cacheDuration;
    
    loggerRef.debug('Durée de cache configurée', { 
      cacheDuration,
      cacheDurationOption: config.cacheDurationOption
    });
    
    const cache = await chrome.storage.local.get(asin);
    if (cache[asin] && Date.now() - cache[asin].timestamp < cacheDuration) {
      loggerRef.cache('Hit cache', { asin, age: (Date.now() - cache[asin].timestamp) / (60 * 60 * 1000) + ' heures' }, 3);
      sendResponse({ cached: true, data: cache[asin].data });
    } else {
      loggerRef.cache('Miss cache', { 
        asin,
        raison: cache[asin] ? 'Expiré' : 'Non trouvé',
        age: cache[asin] ? (Date.now() - cache[asin].timestamp) / (60 * 60 * 1000) + ' heures' : 'N/A'
      }, 3);
      sendResponse({ cached: false });
    }
  } catch (error) {
    loggerRef.error('Erreur lors de la vérification du cache', { 
      asin, 
      error: error.message,
      stack: error.stack 
    });
    sendResponse({ cached: false, error: error.message });
  }
}

// Récupération et analyse des avis - première analyse
async function handleFetch(message, sendResponse) {
  const { asin, locale } = message;
  loggerRef.info('Début de l\'analyse', { asin, locale });
  
  try {
    // Récupérer la configuration pour avoir la durée de cache
    const config = await self.ariConfig.getConfig();
    const cacheDuration = config.cacheDuration;
    const batchSize = config.reviewBatchSize || 20;
    
    // Vérifier le cache
    const cache = await chrome.storage.local.get(asin);
    if (cache[asin] && Date.now() - cache[asin].timestamp < cacheDuration) {
      loggerRef.cache('Hit cache pendant fetchReviews', { 
        asin, 
        age: (Date.now() - cache[asin].timestamp) / (60 * 60 * 1000) + ' heures',
        expiration: new Date(cache[asin].timestamp + cacheDuration).toLocaleString()
      }, 3);
      // Ajouter l'information sur la possibilité de charger plus d'avis
      if (cache[asin].data) {
        cache[asin].data.canLoadMore = cache[asin].data.totalReviewsCount > batchSize;
        cache[asin].data.totalReviewsCount = cache[asin].data.totalReviewsCount || batchSize;
        cache[asin].data.analyzedReviewsCount = cache[asin].data.analyzedReviewsCount || batchSize;
      }
      sendResponse(cache[asin].data);
      return;
    }

    // Récupérer les avis
    let fetchResult;
    try {
      fetchResult = await measureRef(`Récupération des avis pour ${asin}`, 
        () => fetchReviewsBatch(asin, locale, 1, batchSize)
      );
      
      if (!fetchResult.reviews.length) {
        throw new Error("Aucun avis trouvé pour ce produit");
      }
      
      loggerRef.info('Avis récupérés avec succès', { 
        asin, 
        count: fetchResult.reviews.length,
        totalEstimated: fetchResult.totalEstimated,
        sampleFirstReview: fetchResult.reviews[0].substring(0, 50) + '...'
      });
    } catch (error) {
      loggerRef.error('Échec récupération des avis', {
        asin,
        error: error.message,
        stack: error.stack
      });
      throw error; // Propager l'erreur
    }

    // Analyser avec l'IA
    let analysis;
    try {
      analysis = await measureRef(`Analyse IA pour ${asin}`, 
        () => analyzeReviews(fetchResult.reviews, locale)
      );
      
      // Ajouter des métadonnées concernant le nombre d'avis
      analysis.totalReviewsCount = fetchResult.totalEstimated;
      analysis.analyzedReviewsCount = fetchResult.reviews.length;
      analysis.canLoadMore = fetchResult.totalEstimated > fetchResult.reviews.length;
      
      loggerRef.info('Analyse IA complétée', { 
        asin,
        reviewCount: fetchResult.reviews.length,
        prosCount: analysis.pros?.length || 0,
        consCount: analysis.cons?.length || 0,
        canLoadMore: analysis.canLoadMore
      });
    } catch (error) {
      loggerRef.error('Échec analyse IA', {
        asin,
        reviewCount: fetchResult.reviews.length,
        error: error.message,
        stack: error.stack
      });
      throw error; // Propager l'erreur
    }

    // Sauvegarder dans le cache
    try {
      await chrome.storage.local.set({
        [asin]: {
          timestamp: Date.now(),
          data: analysis
        }
      });
      
      loggerRef.cache('Analyse mise en cache', { asin }, 3);
    } catch (error) {
      loggerRef.error('Échec mise en cache', {
        asin,
        error: error.message
      });
      // Ne pas propager cette erreur, c'est non-critique
    }

    sendResponse(analysis);

  } catch (error) {
    loggerRef.error('Échec du traitement', { 
      asin, 
      error: error.message,
      stack: error.stack
    });
    sendResponse({ error: error.message });
  }
}

// Récupération de plus d'avis pour enrichir l'analyse
async function handleFetchMore(message, sendResponse) {
  const { asin, locale } = message;
  loggerRef.info('Enrichissement d\'analyse demandé', { asin, locale });
  
  try {
    // Récupérer le cache actuel
    const cache = await chrome.storage.local.get(asin);
    if (!cache[asin] || !cache[asin].data) {
      throw new Error("Pas d'analyse précédente à enrichir");
    }
    
    const currentAnalysis = cache[asin].data;
    const config = await self.ariConfig.getConfig();
    const batchSize = config.reviewBatchSize || 20;
    
    // Calculer combien d'avis ont déjà été analysés
    const analyzedCount = currentAnalysis.analyzedReviewsCount || batchSize;
    
    // Récupérer le prochain lot d'avis
    const nextPageStart = Math.floor(analyzedCount / 10) + 1; // Amazon affiche ~10 avis par page
    
    let fetchResult;
    try {
      fetchResult = await measureRef(`Récupération d'avis supplémentaires pour ${asin}`, 
        () => fetchReviewsBatch(asin, locale, nextPageStart, batchSize)
      );
      
      if (!fetchResult.reviews.length) {
        throw new Error("Aucun avis supplémentaire trouvé");
      }
      
      loggerRef.info('Avis supplémentaires récupérés', { 
        asin, 
        count: fetchResult.reviews.length,
        totalEstimated: fetchResult.totalEstimated
      });
    } catch (error) {
      loggerRef.error('Échec récupération d\'avis supplémentaires', {
        asin,
        error: error.message
      });
      throw error;
    }

    // Analyser le nouveau lot avec l'IA et fusionner avec l'existant
    let newAnalysis;
    try {
      newAnalysis = await measureRef(`Analyse supplémentaire IA pour ${asin}`, 
        () => analyzeReviews(fetchResult.reviews, locale)
      );
      
      // Fusionner les analyses
      const mergedAnalysis = mergeAnalyses(currentAnalysis, newAnalysis);
      
      // Mettre à jour les métadonnées
      mergedAnalysis.totalReviewsCount = fetchResult.totalEstimated;
      mergedAnalysis.analyzedReviewsCount = analyzedCount + fetchResult.reviews.length;
      mergedAnalysis.canLoadMore = mergedAnalysis.analyzedReviewsCount < fetchResult.totalEstimated;
      
      loggerRef.info('Analyse supplémentaire complétée et fusionnée', { 
        asin,
        totalReviewsCount: mergedAnalysis.totalReviewsCount,
        analyzedReviewsCount: mergedAnalysis.analyzedReviewsCount,
        prosCount: mergedAnalysis.pros?.length || 0,
        consCount: mergedAnalysis.cons?.length || 0
      });
      
      // Mettre à jour le cache
      await chrome.storage.local.set({
        [asin]: {
          timestamp: Date.now(),
          data: mergedAnalysis
        }
      });
      
      sendResponse(mergedAnalysis);
      
    } catch (error) {
      loggerRef.error('Échec analyse supplémentaire', {
        asin,
        error: error.message
      });
      throw error;
    }
    
  } catch (error) {
    loggerRef.error('Échec de l\'enrichissement', { 
      asin, 
      error: error.message,
      stack: error.stack
    });
    sendResponse({ error: error.message });
  }
}

// NOUVELLES FONCTIONS POUR L'ANALYSE DE MARCHÉ

// Gestionnaire pour lancer une analyse de marché
async function handleMarketAnalyze(message, sendResponse) {
  const { keyword, locale = 'com', force = false } = message;
  
  loggerRef.info('Début analyse de marché', { keyword, locale, force });
  
  try {
    // Vérifier si une analyse est déjà en cours pour ce marché
    const marketKey = self.ariConfig.createMarketKey(keyword, locale);
    if (marketAnalysisInProgress.has(marketKey)) {
      loggerRef.warn('Analyse de marché déjà en cours', { keyword, locale });
      sendResponse({ 
        inProgress: true, 
        status: 'already_running',
        progress: marketAnalysisInProgress.get(marketKey)
      });
      return;
    }
    
    // Vérifier le cache si on ne force pas l'analyse
    if (!force) {
      const cachedData = await self.ariConfig.getMarketData(keyword, locale);
      if (cachedData.data && !cachedData.expired) {
        loggerRef.info('Données de marché trouvées en cache', { 
          keyword, 
          locale, 
          age: cachedData.age,
          itemCount: cachedData.data.items?.length || 0
        });
        sendResponse({ 
          cached: true, 
          data: cachedData.data,
          age: cachedData.age
        });
        return;
      }
    }
    
    // Initialiser l'analyse de marché
    const analysisId = Date.now().toString();
    const progress = {
      id: analysisId,
      keyword,
      locale,
      startTime: Date.now(),
      status: 'initializing',
      productsFound: 0,
      productsAnalyzed: 0,
      currentPage: 1,
      totalPages: 0,
      error: null
    };
    
    marketAnalysisInProgress.set(marketKey, progress);
    
    // Réponse immédiate pour indiquer que l'analyse a commencé
    sendResponse({ 
      started: true, 
      analysisId,
      status: 'initializing'
    });
    
    // Lancer l'analyse en arrière-plan
    performMarketAnalysis(keyword, locale, marketKey, progress);
    
  } catch (error) {
    loggerRef.error('Erreur lors de l\'initialisation de l\'analyse de marché', {
      keyword,
      locale,
      error: error.message,
      stack: error.stack
    });
    
    sendResponse({ error: error.message });
  }
}

// Fonction qui effectue l'analyse de marché complète
async function performMarketAnalysis(keyword, locale, marketKey, progress) {
  try {
    // Étape 1: Récupérer les produits de la recherche Amazon
    progress.status = 'fetching_products';
    updateMarketProgress(marketKey, progress);
    
    const products = await fetchAmazonSearchResults(keyword, locale, progress);
    
    if (products.length === 0) {
      throw new Error('Aucun produit trouvé pour ce mot-clé');
    }
    
    progress.productsFound = products.length;
    progress.status = 'analyzing_products';
    updateMarketProgress(marketKey, progress);
    
    // Étape 2: Analyser les produits en parallèle avec limitation
    const config = await self.ariConfig.getConfig();
    const parallelLimit = config.marketParallelFetches;
    const reviewsPerProduct = config.marketReviewsPerProduct;
    
    // Préparer le résultat final
    const marketData = {
      ts: Date.now(),
      keyword,
      locale,
      items: [],
      stats: {},
      completed: false
    };
    
    // Diviser les produits en lots pour les traiter avec limitation de parallélisme
    const chunks = [];
    for (let i = 0; i < products.length; i += parallelLimit) {
      chunks.push(products.slice(i, i + parallelLimit));
    }
    
    // Variable pour suivre si une erreur 503 a été rencontrée
    let encountered503 = false;
    
    // Traiter les produits par lot
    for (const chunk of chunks) {
      if (encountered503) break;
      
      // Vérifier si l'analyse a été annulée
      if (!marketAnalysisInProgress.has(marketKey)) {
        loggerRef.warn('Analyse de marché annulée', { keyword, locale });
        return;
      }
      
      // Analyser chaque produit du lot en parallèle
      const chunkPromises = chunk.map(async (product) => {
        try {
          // Vérifier si l'analyse a été annulée
          if (!marketAnalysisInProgress.has(marketKey)) return null;
          
          loggerRef.info('Analyse du produit', {
            asin: product.asin,
            title: product.title.substring(0, 30) + '...'
          });
          
          // Récupérer et analyser les avis
          const reviews = await fetchReviewsBatch(product.asin, locale, 1, reviewsPerProduct);
          
          // Si on reçoit une réponse 503, signaler le blocage et arrêter
          if (reviews.status === 503) {
            encountered503 = true;
            progress.status = 'blocked';
            progress.error = 'Amazon bloque les requêtes, veuillez réessayer plus tard';
            updateMarketProgress(marketKey, progress);
            return null;
          }
          
          // Analyser les avis avec l'IA
          const analysis = await analyzeReviews(reviews.reviews, locale);
          
          // Créer un hash pour les avis
          const reviewText = reviews.reviews.join('\n').substring(0, 5000);
          const reviewHash = await self.ariConfig.generateSHA256(reviewText);
          
          // Stocker les avis analysés séparément
          const reviewKey = self.ariConfig.createReviewKey(reviewHash);
          await chrome.storage.local.set({
            [reviewKey]: {
              ts: Date.now(),
              prosCons: analysis,
              count: reviews.reviews.length
            }
          });
          
          // Créer l'objet de produit analysé
          const analyzedProduct = {
            ...product,
            reviewCount: reviews.totalEstimated,
            analyzedReviews: reviews.reviews.length,
            analysis: {
              pros: analysis.pros || [],
              cons: analysis.cons || []
            },
            reviewHash
          };
          
          // Incrémenter le compteur de produits analysés
          progress.productsAnalyzed++;
          updateMarketProgress(marketKey, progress);
          
          return analyzedProduct;
          
        } catch (error) {
          loggerRef.error('Erreur lors de l\'analyse du produit', {
            asin: product.asin,
            error: error.message
          });
          
          // Continuer même en cas d'erreur sur un produit
          return {
            ...product,
            error: error.message
          };
        }
      });
      
      // Attendre que tous les produits du lot soient analysés
      const analyzedChunk = await Promise.all(chunkPromises);
      
      // Ajouter les produits analysés au résultat final
      marketData.items = [...marketData.items, ...analyzedChunk.filter(p => p !== null)];
      
      // Mettre à jour les statistiques de marché
      updateMarketStats(marketData);
      
      // Sauvegarder l'état intermédiaire
      await self.ariConfig.saveMarketData(keyword, locale, {
        ...marketData,
        lastUpdate: Date.now()
      });
      
      // Attendre un peu pour éviter le blocage
      await delay(1000 + Math.random() * 1000);
    }
    
    // Mise à jour finale des statistiques de marché
    if (!encountered503) {
      marketData.completed = true;
      marketData.completionTime = Date.now();
      updateMarketStats(marketData);
      
      // Sauvegarder les données finales
      await self.ariConfig.saveMarketData(keyword, locale, marketData);
      
      // Mettre à jour le statut final
      progress.status = 'completed';
      progress.endTime = Date.now();
      updateMarketProgress(marketKey, progress);
      
      loggerRef.info('Analyse de marché terminée avec succès', {
        keyword,
        locale,
        productsAnalyzed: marketData.items.length,
        timeElapsed: Math.round((Date.now() - progress.startTime) / 1000) + 's'
      });
    }
    
  } catch (error) {
    loggerRef.error('Erreur lors de l\'analyse de marché', {
      keyword,
      locale,
      error: error.message,
      stack: error.stack
    });
    
    // Mettre à jour le statut avec l'erreur
    progress.status = 'error';
    progress.error = error.message;
    progress.endTime = Date.now();
    updateMarketProgress(marketKey, progress);
  } finally {
    // Supprimer la référence à l'analyse en cours après un certain délai
    // pour permettre aux clients de récupérer les dernières informations
    setTimeout(() => {
      marketAnalysisInProgress.delete(marketKey);
    }, 60000); // Garder l'info de progression pendant 1 minute
  }
}

// Fonction pour mettre à jour le statut de progression d'une analyse
function updateMarketProgress(marketKey, progress) {
  marketAnalysisInProgress.set(marketKey, {
    ...progress,
    lastUpdate: Date.now()
  });
}

// Récupérer les résultats de recherche Amazon
async function fetchAmazonSearchResults(keyword, locale, progress) {
  const domain = locale === 'fr' ? 'fr' : 'com';
  const encodedKeyword = encodeURIComponent(keyword);
  const products = [];
  const MAX_PAGES = 3; // Limité à 3 pages pour éviter le blocage
  
  try {
    for (let page = 1; page <= MAX_PAGES; page++) {
      // Mettre à jour la progression
      progress.currentPage = page;
      updateMarketProgress(progress.marketKey, progress);
      
      const url = `https://www.amazon.${domain}/s?k=${encodedKeyword}&page=${page}`;
      
      loggerRef.info(`Récupération des produits page ${page}`, { url });
      
      const response = await fetch(url, {
        credentials: 'include',
        headers: {
          'Accept': 'text/html,application/xhtml+xml',
          'Accept-Language': locale === 'fr' ? 'fr-FR' : 'en-US',
          'Cache-Control': 'no-cache',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      });
      
      // Vérifier si Amazon bloque les requêtes
      if (response.status === 503) {
        loggerRef.error('Amazon bloque les requêtes (503)', { page });
        throw new Error('Amazon bloque les requêtes (503), veuillez réessayer plus tard');
      }
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP ${response.status}: ${response.statusText}`);
      }
      
      const html = await response.text();
      
      // Extraire les informations des produits
      const pageProducts = extractProductsFromHtml(html);
      loggerRef.info(`Produits extraits page ${page}`, { count: pageProducts.length });
      
      if (pageProducts.length === 0) {
        loggerRef.warn(`Aucun produit trouvé sur la page ${page}`);
        break; // Sortir si on ne trouve plus de produits
      }
      
      products.push(...pageProducts);
      
      // Mettre à jour le nombre total de produits trouvés
      progress.productsFound = products.length;
      updateMarketProgress(progress.marketKey, progress);
      
      // Vérifier si c'est la dernière page
      if (html.includes('"pagnNextString"') === false) {
        loggerRef.info('Dernière page atteinte', { page });
        break;
      }
      
      // Attendre entre les requêtes pour éviter le blocage
      await delay(1500 + Math.random() * 1000);
    }
    
    // Mettre à jour le nombre total de pages dans la progression
    progress.totalPages = progress.currentPage;
    updateMarketProgress(progress.marketKey, progress);
    
    return products;
    
  } catch (error) {
    loggerRef.error('Erreur lors de la récupération des produits', {
      keyword,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// Extraire les produits du HTML de la page de résultats
function extractProductsFromHtml(html) {
  const products = [];
  
  try {
    // Trouver tous les éléments de produit
    const productRegex = /<div\s+data-asin="([A-Z0-9]{10})"[^>]*?data-component-type="s-search-result"[^>]*?>([\s\S]*?)<\/div>\s*<\/div>\s*<\/div>\s*<\/div>/g;
    let match;
    
    while ((match = productRegex.exec(html)) !== null) {
      const asin = match[1];
      const productHtml = match[2];
      
      if (!asin || asin.length !== 10) continue;
      
      try {
        // Extraire le titre
        const titleMatch = productHtml.match(/<span class="a-size-[^"]*?"[^>]*?>([\s\S]*?)<\/span>/);
        const title = titleMatch ? 
          titleMatch[1]
            .replace(/<[^>]+>/g, '') // Enlever les balises HTML
            .replace(/&nbsp;/g, ' ')  // Remplacer &nbsp;
            .replace(/&amp;/g, '&')   // Remplacer &amp;
            .replace(/\s+/g, ' ')     // Remplacer les espaces multiples
            .trim() : 'Titre non disponible';
        
        // Extraire le prix
        const priceMatch = productHtml.match(/<span class="a-price"[^>]*?><span[^>]*?>([^<]+)<\/span>/);
        let price = 0;
        let currency = '';
        
        if (priceMatch) {
          const priceText = priceMatch[1].trim();
          // Extraire la devise et le prix
          if (priceText.startsWith('€')) {
            currency = 'EUR';
            price = parseFloat(priceText.replace('€', '').replace(',', '.').trim());
          } else if (priceText.startsWith('$')) {
            currency = 'USD';
            price = parseFloat(priceText.replace('$', '').replace(',', '').trim());
          } else {
            // Autre devise
            const currencyMatch = priceText.match(/^([^\d]+)(.+)$/);
            if (currencyMatch) {
              currency = currencyMatch[1].trim();
              price = parseFloat(currencyMatch[2].replace(',', '.').trim());
            }
          }
        }
        
        // Extraire la note
        const ratingMatch = productHtml.match(/([0-9.]+) out of 5 stars|([0-9,]+) étoiles sur 5/);
        let rating = 0;
        if (ratingMatch) {
          rating = parseFloat((ratingMatch[1] || ratingMatch[2]).replace(',', '.'));
        }
        
        // Extraire le nombre d'avis
        const reviewCountMatch = productHtml.match(/([0-9,\s]+) ratings|([0-9\s]+) évaluations/);
        let reviewCount = 0;
        if (reviewCountMatch) {
          reviewCount = parseInt((reviewCountMatch[1] || reviewCountMatch[2])
            .replace(',', '')
            .replace(/\s+/g, '')
            .trim());
        }
        
        // Extraire les caractéristiques (specs)
        const specs = {};
        const specsList = productHtml.match(/<li[^>]*?><span[^>]*?>([\s\S]*?)<\/span><span[^>]*?>([\s\S]*?)<\/span><\/li>/g);
        if (specsList) {
          specsList.forEach(spec => {
            const specMatch = spec.match(/<span[^>]*?>([\s\S]*?)<\/span><span[^>]*?>([\s\S]*?)<\/span>/);
            if (specMatch) {
              const key = specMatch[1]
                .replace(/<[^>]+>/g, '')
                .replace(/&nbsp;/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();
              
              const value = specMatch[2]
                .replace(/<[^>]+>/g, '')
                .replace(/&nbsp;/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();
              
              if (key && value) {
                specs[key] = value;
              }
            }
          });
        }
        
        // Ajouter le produit à la liste
        products.push({
          asin,
          title,
          price,
          currency,
          rating,
          reviewCount,
          specs,
          url: `https://www.amazon.com/dp/${asin}`
        });
        
      } catch (productError) {
        loggerRef.error('Erreur lors de l\'extraction des informations du produit', {
          asin,
          error: productError.message
        });
      }
    }
    
    return products;
    
  } catch (error) {
    loggerRef.error('Erreur lors de l\'extraction des produits du HTML', {
      error: error.message,
      stack: error.stack
    });
    return [];
  }
}

// Mettre à jour les statistiques de marché
function updateMarketStats(marketData) {
  try {
    const items = marketData.items.filter(item => !item.error);
    
    if (items.length === 0) {
      marketData.stats = { isEmpty: true };
      return;
    }
    
    // Calcul des statistiques de prix
    const prices = items
      .filter(item => item.price && item.price > 0)
      .map(item => item.price)
      .sort((a, b) => a - b);
    
    const avgPrice = prices.length > 0 
      ? prices.reduce((sum, price) => sum + price, 0) / prices.length 
      : 0;
    
    const medianPrice = prices.length > 0 
      ? prices[Math.floor(prices.length / 2)] 
      : 0;
    
    const priceRange = prices.length > 0 
      ? [prices[0], prices[prices.length - 1]] 
      : [0, 0];
    
    // Calcul des statistiques de notation
    const ratings = items
      .filter(item => item.rating && item.rating > 0)
      .map(item => item.rating);
    
    const avgRating = ratings.length > 0 
      ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
      : 0;
    
    // Regroupement des points positifs et négatifs les plus mentionnés
    const allPros = [];
    const allCons = [];
    
    items.forEach(item => {
      if (item.analysis) {
        if (item.analysis.pros) {
          allPros.push(...item.analysis.pros);
        }
        if (item.analysis.cons) {
          allCons.push(...item.analysis.cons);
        }
      }
    });
    
    // Agréger les points similaires
    const prosMap = new Map();
    const consMap = new Map();
    
    allPros.forEach(pro => {
      const key = pro.text.toLowerCase();
      if (prosMap.has(key)) {
        prosMap.set(key, {
          text: pro.text,
          count: prosMap.get(key).count + (pro.count || 1)
        });
      } else {
        prosMap.set(key, {
          text: pro.text,
          count: pro.count || 1
        });
      }
    });
    
    allCons.forEach(con => {
      const key = con.text.toLowerCase();
      if (consMap.has(key)) {
        consMap.set(key, {
          text: con.text,
          count: consMap.get(key).count + (con.count || 1)
        });
      } else {
        consMap.set(key, {
          text: con.text,
          count: con.count || 1
        });
      }
    });
    
    // Convertir les maps en tableaux et trier par fréquence
    const mostMentionedPros = Array.from(prosMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    const mostMentionedCons = Array.from(consMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    // Comparaison des spécifications
    const specKeys = new Set();
    items.forEach(item => {
      if (item.specs) {
        Object.keys(item.specs).forEach(key => specKeys.add(key));
      }
    });
    
    const specComparison = {};
    specKeys.forEach(key => {
      specComparison[key] = items
        .filter(item => item.specs && item.specs[key])
        .map(item => ({
          asin: item.asin,
          value: item.specs[key]
        }));
    });
    
    // Mettre à jour les statistiques dans les données de marché
    marketData.stats = {
      totalProducts: items.length,
      avgPrice,
      medianPrice,
      priceRange,
      avgRating,
      mostMentionedPros,
      mostMentionedCons,
      specComparison,
      updatedAt: Date.now()
    };
    
  } catch (error) {
    loggerRef.error('Erreur lors de la mise à jour des statistiques', {
      error: error.message,
      stack: error.stack
    });
    marketData.stats = {
      error: error.message
    };
  }
}

// Gestionnaire pour récupérer l'état d'une analyse de marché
async function handleMarketGetData(message, sendResponse) {
  const { keyword, locale = 'com' } = message;
  
  try {
    const marketKey = self.ariConfig.createMarketKey(keyword, locale);
    
    // Vérifier si une analyse est en cours
    if (marketAnalysisInProgress.has(marketKey)) {
      const progress = marketAnalysisInProgress.get(marketKey);
      sendResponse({
        inProgress: true,
        progress
      });
      return;
    }
    
    // Si pas d'analyse en cours, vérifier les données en cache
    const cachedData = await self.ariConfig.getMarketData(keyword, locale);
    
    if (cachedData.data) {
      loggerRef.info('Données de marché récupérées du cache', {
        keyword,
        locale,
        age: cachedData.age,
        expired: cachedData.expired || false,
        itemCount: cachedData.data.items?.length || 0
      });
      
      sendResponse({
        cached: true,
        data: cachedData.data,
        age: cachedData.age,
        expired: cachedData.expired || false
      });
    } else {
      sendResponse({
        cached: false,
        exists: false
      });
    }
  } catch (error) {
    loggerRef.error('Erreur lors de la récupération des données de marché', {
      keyword,
      locale,
      error: error.message,
      stack: error.stack
    });
    
    sendResponse({ error: error.message });
  }
}

// Gestionnaire pour récupérer uniquement les produits d'un mot-clé
async function handleMarketFetchProducts(message, sendResponse) {
  const { keyword, locale = 'com' } = message;
  
  try {
    const progress = {
      marketKey: self.ariConfig.createMarketKey(keyword, locale),
      startTime: Date.now(),
      status: 'fetching_products',
      productsFound: 0,
      currentPage: 1
    };
    
    const products = await fetchAmazonSearchResults(keyword, locale, progress);
    
    loggerRef.info('Produits récupérés avec succès', {
      keyword,
      locale,
      count: products.length
    });
    
    sendResponse({
      success: true,
      products,
      count: products.length
    });
    
  } catch (error) {
    loggerRef.error('Erreur lors de la récupération des produits', {
      keyword,
      locale,
      error: error.message,
      stack: error.stack
    });
    
    sendResponse({
      error: error.message
    });
  }
}

// Gestionnaire pour annuler une analyse de marché en cours
async function handleMarketCancelAnalysis(message, sendResponse) {
  const { keyword, locale = 'com' } = message;
  const marketKey = self.ariConfig.createMarketKey(keyword, locale);
  
  if (marketAnalysisInProgress.has(marketKey)) {
    const progress = marketAnalysisInProgress.get(marketKey);
    progress.status = 'cancelled';
    progress.endTime = Date.now();
    
    // Supprimer après un court délai pour permettre au client de voir le statut "cancelled"
    setTimeout(() => {
      marketAnalysisInProgress.delete(marketKey);
    }, 5000);
    
    loggerRef.info('Analyse de marché annulée', { keyword, locale });
    sendResponse({ success: true, status: 'cancelled' });
  } else {
    sendResponse({ success: false, error: 'Aucune analyse en cours pour ce marché' });
  }
}

// Fonction pour fusionner deux analyses
function mergeAnalyses(analysis1, analysis2) {
  // Fonction utilitaire pour fusionner des listes de points avec déduplication
  function mergePoints(points1, points2) {
    const mergedMap = new Map();
    
    // Ajouter les points de la première analyse
    points1.forEach(point => {
      mergedMap.set(point.text.toLowerCase(), point);
    });
    
    // Fusionner avec les points de la seconde analyse
    points2.forEach(point => {
      const key = point.text.toLowerCase();
      if (mergedMap.has(key)) {
        // Fusionner les compteurs
        mergedMap.get(key).count += point.count;
      } else {
        // Ajouter le nouveau point
        mergedMap.set(key, point);
      }
    });
    
    // Convertir la Map en tableau et trier par compte décroissant
    return Array.from(mergedMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Limiter à 5 points maximum
  }
  
  return {
    ...analysis1,
    pros: mergePoints(analysis1.pros || [], analysis2.pros || []),
    cons: mergePoints(analysis1.cons || [], analysis2.cons || [])
  };
}

// Récupération d'un lot d'avis spécifique
async function fetchReviewsBatch(asin, locale, startPage, batchSize) {
  const reviews = [];
  const domain = locale === 'fr' ? 'fr' : 'com';
  const MAX_PAGES = 5; // Limitation pour éviter le blocage
  const seenReviews = new Set(); // Pour éviter les doublons
  let totalEstimatedReviews = 0;

  loggerRef.info(`Récupération des avis (lot)`, { 
    asin, 
    locale,
    domain,
    startPage,
    batchSize
  });
  
  let hasMorePages = true;
  let currentPage = startPage;

  while (reviews.length < batchSize && currentPage <= startPage + MAX_PAGES - 1 && hasMorePages) {
    const url = `https://www.amazon.${domain}/product-reviews/${asin}?reviewerType=all_reviews&sortBy=recent&pageNumber=${currentPage}`;
    loggerRef.debug(`Récupération page ${currentPage}`, { url });
    
    try {
      const response = await fetch(url, {
        credentials: 'include',
        headers: {
          'Accept': 'text/html,application/xhtml+xml',
          'Accept-Language': locale === 'fr' ? 'fr-FR' : 'en-US',
          'Cache-Control': 'no-cache',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        loggerRef.error(`Erreur HTTP page ${currentPage}`, {
          status: response.status,
          statusText: response.statusText,
          url
        });
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      loggerRef.debug(`Page ${currentPage} récupérée`, { 
        url,
        size: html.length,
        statusCode: response.status
      });
      
      // Estimer le nombre total d'avis
      if (currentPage === startPage) {
        // Chercher le compteur de reviews total
        const totalMatch = html.match(/([0-9,]+) total ratings/);
        if (totalMatch && totalMatch[1]) {
          totalEstimatedReviews = parseInt(totalMatch[1].replace(/,/g, ''), 10);
        } else {
          // Alternative en français ou autre format
          const altMatch = html.match(/([0-9\s]+)\s+évaluation/i);
          if (altMatch && altMatch[1]) {
            totalEstimatedReviews = parseInt(altMatch[1].replace(/\s/g, ''), 10);
          }
        }
        
        loggerRef.debug(`Estimation nombre total d'avis`, {
          totalEstimated: totalEstimatedReviews || 'Non trouvé'
        });
      }

      // Méthode 1: .review-text-content span (celle qui a fonctionné dans la console)
      const regex1 = /class="review-text-content"[^>]*>[\s\n]*<span[^>]*>([^<]+)<\/span>/g;
      let match1;
      let pageReviews = [];
      
      while ((match1 = regex1.exec(html)) !== null) {
        const text = match1[1].trim();
        if (text.length > 10 && !seenReviews.has(text)) {
          pageReviews.push(text);
          seenReviews.add(text);
        }
      }
      
      // Si aucun avis trouvé avec la première méthode, essayer d'autres méthodes
      if (pageReviews.length === 0) {
        loggerRef.debug('Méthode 1 n\'a trouvé aucun avis, essai méthode 2', { page: currentPage });
        
        // Méthode 2: data-hook="review-body"
        const regex2 = /data-hook="review-body"[^>]*>(.*?)<\/span>/gs;
        let match2;
        while ((match2 = regex2.exec(html)) !== null) {
          const rawText = match2[1];
          const text = rawText.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
          if (text.length > 10 && !seenReviews.has(text)) {
            pageReviews.push(text);
            seenReviews.add(text);
          }
        }
        
        // Méthode 3: cr-original-review-content
        if (pageReviews.length === 0) {
          loggerRef.debug('Méthode 2 n\'a trouvé aucun avis, essai méthode 3', { page: currentPage });
          
          const regex3 = /class="cr-original-review-content"[^>]*>(.*?)<\/span>/gs;
          let match3;
          while ((match3 = regex3.exec(html)) !== null) {
            const rawText = match3[1];
            const text = rawText.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
            if (text.length > 10 && !seenReviews.has(text)) {
              pageReviews.push(text);
              seenReviews.add(text);
            }
          }
        }
      }
      
      loggerRef.info(`Avis extraits page ${currentPage}`, { count: pageReviews.length });
      
      // Si aucun avis n'est trouvé sur cette page
      if (pageReviews.length === 0) {
        if (currentPage === startPage && reviews.length === 0) {
          // Vérifier si le produit n'a réellement pas d'avis
          const noReviews = html.includes('cr-lighthouse-no-reviews-section') || 
                           html.includes('class="noReviews"') ||
                           html.includes('Aucun avis client');
          if (noReviews) {
            loggerRef.info('Message "aucun avis" détecté', { asin, page: currentPage });
          }
        }
        loggerRef.debug(`Aucun avis trouvé sur la page ${currentPage}, arrêt`, { asin });
        hasMorePages = false;
        break;
      }
      
      // Ajouter les avis de cette page
      reviews.push(...pageReviews);
      
      // Vérifier si c'est la dernière page
      if (html.includes('class="a-disabled a-last"')) {
        loggerRef.debug(`Dernière page détectée (${currentPage})`, { asin });
        hasMorePages = false;
        break;
      }
      
      // Passer à la page suivante
      currentPage++;
      
      // Si on a assez d'avis, s'arrêter
      if (reviews.length >= batchSize) {
        break;
      }
      
      // Pause entre les requêtes pour éviter le blocage
      await delay(800 + Math.random() * 700);
      
    } catch (error) {
      loggerRef.error(`Erreur lors de la récupération de la page ${currentPage}`, { 
        asin, 
        page: currentPage,
        error: error.message,
        stack: error.stack 
      });
      break;
    }
  }

  loggerRef.info(`Total des avis récupérés (lot)`, { 
    asin, 
    count: reviews.length,
    uniqueCount: seenReviews.size,
    totalEstimated: totalEstimatedReviews || 'inconnu'
  });
  
  // Si aucun avis trouvé et que c'était la première tentative
  if (reviews.length === 0 && startPage === 1) {
    // Dernière tentative: essayer directement la page du produit
    try {
      loggerRef.debug('Tentative sur la page produit directement', { asin });
      const productUrl = `https://www.amazon.${domain}/dp/${asin}`;
      const response = await fetch(productUrl, { credentials: 'include' });
      if (response.ok) {
        const html = await response.text();
        
        // Estimer le nombre total d'avis
        const totalMatch = html.match(/([0-9,]+) total ratings/);
        if (totalMatch && totalMatch[1]) {
          totalEstimatedReviews = parseInt(totalMatch[1].replace(/,/g, ''), 10);
        }
        
        // Extraire les avis avec la même méthode
        const regex = /class="review-text-content"[^>]*>[\s\n]*<span[^>]*>([^<]+)<\/span>/g;
        let match;
        while ((match = regex.exec(html)) !== null) {
          const text = match[1].trim();
          if (text.length > 10 && !seenReviews.has(text)) {
            reviews.push(text);
            seenReviews.add(text);
          }
        }
        loggerRef.info(`Avis trouvés sur page produit`, { count: reviews.length });
      }
    } catch (error) {
      loggerRef.error('Échec de la tentative sur page produit', {
        asin,
        error: error.message,
        stack: error.stack
      });
    }
  }
  
  if (reviews.length === 0) {
    loggerRef.error('Aucun avis trouvé pour ce produit', { asin });
    throw new Error('Aucun avis trouvé pour ce produit');
  }
  
  // Limiter les reviews au nombre demandé
  const limitedReviews = reviews.slice(0, batchSize);
  
  return {
    reviews: limitedReviews,
    totalEstimated: totalEstimatedReviews || limitedReviews.length,
    hasMorePages
  };
}

// Analyse avec l'IA
async function analyzeReviews(reviews, locale) {
  loggerRef.info('Début analyse IA', { reviewCount: reviews.length });
  
  // Log des premiers avis pour debug
  if (reviews.length > 0) {
    loggerRef.debug(`Échantillon d'avis`, reviews.slice(0, 3).map(r => r.substring(0, 50) + '...'));
  }

  try {
    const requestBody = {
      model: "deepseek/deepseek-r1:free",
      messages: [{
        role: "user",
        content: `Analysez ces avis et identifiez les points positifs (pros) et négatifs (cons) les plus mentionnés.
                 Répondez uniquement en JSON avec la structure : 
                 {"pros":[{"text":"point","count":n}],"cons":[{"text":"point","count":n}]}
                 Limitez à 3 points par catégorie, en ${locale === 'fr' ? 'français' : 'english'}.
                 IMPORTANT: Ne pas inclure de backticks ni de marqueurs markdown dans la réponse.
                 Les avis : ${reviews.slice(0, 200).join('\n')}`
      }]
    };
    
    loggerRef.debug('Envoi requête OpenRouter', { 
      model: requestBody.model, 
      reviewsCount: reviews.length,
      contentLength: requestBody.messages[0].content.length
    });

    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
        "HTTP-Referer": "https://github.com/amazon-review-insights",
        "X-Title": "Amazon Review Insights",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(requestBody)
    });

    loggerRef.debug(`Réponse OpenRouter Status`, { status: response.status, statusText: response.statusText });

    if (!response.ok) {
      const errorText = await response.text();
      loggerRef.error('Erreur OpenRouter', { 
        status: response.status, 
        statusText: response.statusText, 
        body: errorText
      });
      throw new Error(`Erreur API: HTTP ${response.status} - ${response.statusText}`);
    }

    const result = await response.json();
    loggerRef.debug(`Réception réponse OpenRouter`, {
      id: result.id,
      model: result.model,
      hasChoices: !!result.choices
    });
    
    if (!result.choices?.[0]?.message?.content) {
      loggerRef.error('Réponse IA invalide', result);
      throw new Error("Réponse IA invalide (pas de contenu)");
    }

    try {
      // Log du contenu brut pour debug
      const rawContent = result.choices[0].message.content;
      loggerRef.debug(`Contenu brut reçu`, { 
        content: rawContent.substring(0, 200) + (rawContent.length > 200 ? '...' : '') 
      });
      
      // Nettoyer le contenu des marqueurs markdown et autres caractères indésirables
      const cleanContent = rawContent
        .replace(/```json\s*/g, '')  // Enlever ```json
        .replace(/```\s*/g, '')      // Enlever ```
        .replace(/^\s*|\s*$/g, '')   // Trim
        .replace(/\\n/g, ' ')        // Remplacer \n par espace
        .replace(/\\/g, '')          // Enlever les backslashes restants
        .replace(/^"|"$/g, '');      // Enlever les guillemets au début et à la fin si présents
      
      loggerRef.debug(`Contenu nettoyé`, { 
        content: cleanContent.substring(0, 200) + (cleanContent.length > 200 ? '...' : '') 
      });
      
      // Tentative de parse du JSON
      let analysis;
      try {
        analysis = JSON.parse(cleanContent);
      } catch (parseError) {
        // Si le parsing échoue, essayer de nettoyer davantage
        const extraCleanContent = cleanContent
          .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":') // Assurer que les clés sont entre guillemets
          .replace(/'/g, '"'); // Remplacer les guillemets simples par des doubles
        
        loggerRef.debug('Tentative de parsing avec nettoyage supplémentaire', {
          extraClean: extraCleanContent.substring(0, 200)
        });
        
        analysis = JSON.parse(extraCleanContent);
      }
      
      // Validation des données reçues
      if (!Array.isArray(analysis.pros) || !Array.isArray(analysis.cons)) {
        loggerRef.error('Structure JSON invalide', analysis);
        throw new Error("Réponse mal structurée (pros/cons non trouvés)");
      }
      
      loggerRef.info('Analyse IA réussie', { 
        pros: analysis.pros.length,
        cons: analysis.cons.length,
        prosExample: analysis.pros.length > 0 ? analysis.pros[0].text : 'aucun',
        consExample: analysis.cons.length > 0 ? analysis.cons[0].text : 'aucun'
      });
      
      return analysis;
    } catch (error) {
      loggerRef.error('Erreur parsing réponse', {
        error: error.message,
        rawContentSample: result.choices[0].message.content.substring(0, 100)
      });
      throw error;
    }
  } catch (error) {
    loggerRef.error('Erreur analyse IA', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}