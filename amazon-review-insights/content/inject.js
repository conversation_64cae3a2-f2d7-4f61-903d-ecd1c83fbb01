// Configuration de logs (sera remplacée par celle de common.js)
// Utiliser une variable qui n'entrera pas en conflit
var ariContentLogger; // Utiliser var au lieu de let pour la portée

// Indicateur de version pour vérifier le chargement
console.log('Amazon Review Insights injecté v0.2.1 (Mode Auto/Manuel) -', new Date().toISOString());

// Variables globales pour la détection de changement de page
let lastPageProcessed = Date.now();
let currentPageUrl = window.location.href;  // Ajout de la déclaration manquante
// Variables globales pour la gestion du tooltip
let activeTooltip = null;
let isTooltipActive = false;

// Initialiser le logger une fois que le script common.js est chargé
function initLogger() {
  // Attendre que l'objet global ariLogger soit disponible
  if (window.ariLogger) {
    ariContentLogger = window.ariLogger.logger;
    
    // Configurer le niveau de log pour plus de détails
    window.ariLogger.getLogConfig().then(config => {
      if (config.logLevel < 4) { // 4 est le niveau debug
        window.ariLogger.saveLogConfig({
          ...config,
          logLevel: 4,
          logToConsole: true
        }).then(() => {
          ariContentLogger.info('Configuration des logs mise à jour pour le débogage', { 
            newLogLevel: 4,
            version: '0.2.0'
          });
        });
      }
    });
    
    return true;
  } else {
    // Fallback si ariLogger n'est pas disponible
    console.warn('ARI: Logger centralisé non disponible, utilisation du logger de secours');
    ariContentLogger = {
      error: (msg, data = {}) => console.error(`❌ [ARI] ${msg}`, data),
      warn: (msg, data = {}) => console.warn(`⚠️ [ARI] ${msg}`, data),
      info: (msg, data = {}) => console.log(`📝 [ARI] ${msg}`, data),
      debug: (msg, data = {}) => console.log(`🔍 [ARI] ${msg}`, data),
      badge: (msg, data = {}) => console.log(`🔖 [ARI] ${msg}`, data),
      cache: (msg, data = {}) => console.log(`💾 [ARI] ${msg}`, data),
      event: (msg, data = {}) => console.log(`🔔 [ARI] ${msg}`, data),
    };
    return false;
  }
}

// Fonction de vérification d'initialisation avec réessais
function ensureLoggerInitialized() {
  if (ariContentLogger) {
    return Promise.resolve(true);
  }
  
  // Premier essai d'initialisation
  if (initLogger()) {
    return Promise.resolve(true);
  }
  
  // Si l'initialisation échoue, on essaie à nouveau quelques fois
  let attempts = 0;
  const maxAttempts = 5;
  
  return new Promise(resolve => {
    const checkInterval = setInterval(() => {
      attempts++;
      if (initLogger() || attempts >= maxAttempts) {
        clearInterval(checkInterval);
        resolve(!!ariContentLogger);
      }
    }, 200);
  });
}

// Constantes
const BADGE_HTML = `<span class="ari-badge inactive" data-ari-version="0.2.1">R</span>`;
const TOOLTIP_ID = 'ari-insights-tooltip';

// Système de gestion de file d'attente pour les analyses
class AnalysisQueue {
  constructor() {
    this.queue = [];
    this.processing = new Set();
    this.config = null;
    // Mode automatique selon la configuration
    this.isAutoAnalysisRunning = false;
    this.initialAnalysisCount = 0;
    this.loadConfig();
  }

  async loadConfig() {
    try {
      // Récupérer la configuration depuis le service worker
      const response = await chrome.runtime.sendMessage({ type: 'get_config' });
      
      if (response && response.config) {
        this.config = response.config;
        // Utiliser la valeur de la configuration pour le mode auto
        this.isAutoAnalysisRunning = this.config.autoAnalysisEnabled;
        
        ariContentLogger.info('Configuration chargée', {
          maxParallel: this.config.maxParallelAnalyses,
          autoMode: this.config.autoAnalysisEnabled
        });
      } else {
        // Valeurs par défaut si la configuration n'est pas disponible
        this.config = {
          maxParallelAnalyses: 5,
          autoAnalysisEnabled: false,
          autoAnalysisDelay: 1000,
          prioritizeVisibleProducts: true
        };
        ariContentLogger.warn('Impossible de récupérer la configuration, utilisation des valeurs par défaut', {
          error: response?.error || 'Aucune réponse'
        });
      }
    } catch (error) {
      ariContentLogger.error('Erreur lors du chargement de la configuration', {
        error: error.message,
        stack: error.stack
      });
      
      // Configuration par défaut en cas d'erreur
      this.config = {
        maxParallelAnalyses: 5,
        autoAnalysisEnabled: false,
        autoAnalysisDelay: 1000,
        prioritizeVisibleProducts: true
      };
    }
  }

  // Méthode améliorée pour ajouter un badge à la file d'attente
  // Cette méthode centralisée respecte les limites configurées
  async addWithLimits(badge, asin, priority = 0) {
    // Ne pas ajouter si déjà dans la file ou en cours de traitement
    if (this.isQueued(asin) || this.isProcessing(asin)) {
      ariContentLogger.debug('ASIN déjà dans la file d\'attente ou en cours de traitement', { asin });
      return false;
    }
    
    // Vérifier si nous avons atteint la limite d'analyses parallèles configurée
    if (this.queue.length + this.processing.size >= (this.config.maxParallelAnalyses || 5)) {
      ariContentLogger.debug('Limite de parallélisme atteinte, badge non ajouté', {
        asin,
        queueLength: this.queue.length,
        processing: this.processing.size,
        max: this.config.maxParallelAnalyses || 5
      });
      return false;
    }
    
    // Ajouter à la file d'attente
    const queueItem = { badge, asin, priority, addedAt: Date.now() };
    this.queue.push(queueItem);
    
    ariContentLogger.debug('ASIN ajouté à la file d\'attente', { 
      asin, 
      priority,
      queueLength: this.queue.length
    });
    
    // Mettre à jour visuellement le badge pour montrer qu'il est en attente de traitement
    updateBadgeState(badge, 'loading');
    
    // Trier la file par priorité (plus élevée d'abord)
    this.sortQueue();
    
    // Si c'est le premier élément à être ajouté à la file et qu'aucun traitement n'est en cours,
    // on force le démarrage immédiat de l'analyse
    if (this.queue.length === 1 && this.processing.size === 0) {
      ariContentLogger.info('Premier badge ajouté - démarrage forcé de l\'analyse', {
        asin,
        priority,
        queueLength: this.queue.length
      });
      
      // On utilise setTimeout pour s'assurer que cette action est effectuée après le retour de cette méthode
      setTimeout(() => this.processNext(), 50);
    } else {
      // Sinon, on essaie de démarrer normalement si possible
      this.processNext();
    }
    
    return true;
  }
  
  // On garde l'ancienne méthode add pour compatibilité mais elle appelle maintenant addWithLimits
  async add(badge, asin, priority = 0) {
    return this.addWithLimits(badge, asin, priority);
  }

  isQueued(asin) {
    return this.queue.some(item => item.asin === asin);
  }

  isProcessing(asin) {
    return this.processing.has(asin);
  }

  sortQueue() {
    // Trier par priorité (plus élevée d'abord) puis par ancienneté
    this.queue.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      return a.addedAt - b.addedAt;
    });
  }

  async processNext() {
    try {
      // Si déjà au max des analyses parallèles, ne rien faire
      if (this.processing.size >= this.config.maxParallelAnalyses) {
        ariContentLogger.debug('Limite d\'analyses parallèles atteinte', {
          processing: this.processing.size,
          max: this.config.maxParallelAnalyses
        });
        return;
      }
      
      // Si la file est vide, ne rien faire (mode manuel uniquement)
      if (this.queue.length === 0) {
        ariContentLogger.debug('File d\'attente vide');
        return;
      }
      
      // Prendre le premier élément de la file
      const item = this.queue.shift();
      const { badge, asin } = item;
      
      // Marquer comme en cours de traitement
      this.processing.add(asin);
      
      ariContentLogger.info('Démarrage de l\'analyse depuis la file', {
        asin,
        remainingQueue: this.queue.length,
        processing: this.processing.size
      });
      
      // MODIFICATION: S'assurer que le badge est en état de chargement
      updateBadgeState(badge, 'loading');
      
      // Démarrer l'analyse
      try {
        await startAnalysis(badge, asin);
      } catch (error) {
        ariContentLogger.error('Erreur lors de l\'analyse depuis la file', {
          asin,
          error: error.message,
          stack: error.stack
        });
        
        // MODIFICATION: En cas d'erreur, remettre le badge en état inactif
        updateBadgeState(badge, 'inactive');
      } finally {
        // Retirer de la liste des traitements en cours
        this.processing.delete(asin);
        
        // MODIFICATION: Planifier le traitement du prochain élément après un court délai
        // pour éviter les blocages potentiels
        setTimeout(() => this.processNext(), 100);
      }
    } catch (error) {
      ariContentLogger.error('Erreur dans processNext', {
        error: error.message,
        stack: error.stack
      });
      
      // Sécurité: attendre un peu avant de réessayer
      setTimeout(() => this.processNext(), 1000);
    }
  }
  
  // Mode d'analyse automatique
  stopAutoAnalysis() {
    this.isAutoAnalysisRunning = false;
    ariContentLogger.info('Mode d\'analyse automatique désactivé en permanence');
  }
  
  clear() {
    const queueSize = this.queue.length;
    this.queue = [];
    ariContentLogger.info('File d\'attente vidée', { itemsRemoved: queueSize });
  }
}

// Créer l'instance globale de la file d'attente
const analysisQueue = new AnalysisQueue();

// Mise à jour des états des badges
function updateBadgeState(badge, state) {
  const oldState = Array.from(badge.classList).find(c => 
    ['inactive', 'loading', 'processing', 'ready', 'disabled'].includes(c)
  );
  
  badge.classList.remove('inactive', 'loading', 'processing', 'ready', 'disabled');
  badge.classList.add(state);
  
  ariContentLogger.badge('État du badge mis à jour', { 
    asin: badge.closest('.ari-badge-container')?.dataset.ariAsin,
    from: oldState,
    to: state
  });
}

// Génération du contenu du tooltip
function generateTooltipContent(data) {
  ariContentLogger.debug('Génération du contenu du tooltip');
  
  if (!data || !data.summary) {
    return '<div class="ari-error">Erreur lors de l\'analyse des avis.</div>';
  }
  
  const summary = data.summary;
  let content = `<div class="ari-tooltip-content">`;
  
  // Inclure le titre et les statistiques
  content += `
    <div class="ari-header">Analyse des avis Amazon</div>
    <div class="ari-stats">
      <div class="ari-stat">
        <span class="ari-stat-label">ASIN:</span>
        <span class="ari-stat-value">${data.asin}</span>
      </div>
      <div class="ari-stat">
        <span class="ari-stat-label">Avis analysés:</span>
        <span class="ari-stat-value">${data.reviewCount}</span>
      </div>
    </div>
  `;
  
  // Affichage du sentiment général
  content += `
    <div class="ari-sentiment">
      <div class="ari-sentiment-label">Sentiment général:</div>
      <div class="ari-sentiment-value ${summary.overallSentiment.toLowerCase()}">${summary.overallSentiment}</div>
    </div>
  `;
  
  // Affichage des points forts et des points faibles
  content += '<div class="ari-highlights">';
  if (summary.pros && summary.pros.length > 0) {
    content += '<div class="ari-pros"><h3>Points forts</h3><ul>';
    summary.pros.forEach(pro => {
      content += `<li>${pro}</li>`;
    });
    content += '</ul></div>';
  }
  
  if (summary.cons && summary.cons.length > 0) {
    content += '<div class="ari-cons"><h3>Points faibles</h3><ul>';
    summary.cons.forEach(con => {
      content += `<li>${con}</li>`;
    });
    content += '</ul></div>';
  }
  content += '</div>';
  
  // Informations sur le nombre d'avis analysés
  content += `
    <div class="ari-review-count">
      Basé sur l'analyse de ${data.reviewCount} avis.
    </div>
  `;
  
  // Ajouter un bouton "Analyser plus d'avis" si hasMoreReviews est vrai
  if (data.hasMoreReviews) {
    content += `
      <div class="ari-load-more-container">
        <button class="ari-load-more" data-asin="${data.asin}">Analyser plus d'avis</button>
      </div>
    `;
  }
  
  content += `</div>`;
  return content;
}

// Affichage du tooltip avec meilleure gestion de la position et du timing
function showTooltip(badge, data) {
  const asin = badge.closest('.ari-badge-container')?.dataset.ariAsin;
  const state = Array.from(badge.classList).find(c => 
    ['inactive', 'loading', 'processing', 'ready'].includes(c)
  );

  ariContentLogger.event('Affichage du tooltip', { asin, state });

  // Supprimer l'ancien tooltip s'il existe
  const oldTooltip = document.getElementById(TOOLTIP_ID);
  if (oldTooltip) oldTooltip.remove();

  // Créer le nouveau tooltip
  const tt = document.createElement('div');
  tt.id = TOOLTIP_ID;
  if (data.message || data.error) tt.className = 'status';
  tt.innerHTML = generateTooltipContent(data);
  document.body.appendChild(tt);

  // Mettre à jour les variables globales de suivi du tooltip
  activeTooltip = tt;
  isTooltipActive = true;

  // Calcul de la position optimale
  const badgeRect = badge.getBoundingClientRect();
  const tooltipWidth = tt.offsetWidth;
  const viewportWidth = window.innerWidth;
  const scrollLeft = window.pageXOffset;
  const scrollTop = window.pageYOffset;

  // Position horizontale
  let leftPos = badgeRect.left + scrollLeft;
  // Si le tooltip dépasse à droite
  if (leftPos + tooltipWidth > viewportWidth + scrollLeft) {
    leftPos = viewportWidth + scrollLeft - tooltipWidth - 20; // 20px de marge
  }
  // Si le tooltip dépasse à gauche
  if (leftPos < scrollLeft) {
    leftPos = scrollLeft + 20; // 20px de marge
  }

  // Position verticale avec vérification de dépassement
  let topPos = badgeRect.bottom + scrollTop + 8;
  const tooltipHeight = tt.offsetHeight;
  // Si le tooltip dépasse en bas
  if (topPos + tooltipHeight > window.innerHeight + scrollTop) {
    topPos = badgeRect.top + scrollTop - tooltipHeight - 8;
  }

  tt.style.left = `${leftPos}px`;
  tt.style.top = `${topPos}px`;

  // Variables pour la gestion du hover
  let isMouseOverBadge = false;
  let isMouseOverTooltip = false;

  // Fonction pour fermer le tooltip
  const closeTooltip = () => {
    if (!isMouseOverBadge && !isMouseOverTooltip) {
      isTooltipActive = false;  // Désactiver le suivi global
      tt.style.opacity = '0';
      setTimeout(() => {
        if (!isMouseOverBadge && !isMouseOverTooltip) {
          tt.remove();
          // Réinitialiser les références globales
          if (activeTooltip === tt) {
            activeTooltip = null;
          }
        }
      }, 150);
    }
  };

  // Gestion du hover sur le badge
  badge.addEventListener('mouseenter', () => {
    isMouseOverBadge = true;
    isTooltipActive = true;
  });

  badge.addEventListener('mouseleave', () => {
    isMouseOverBadge = false;
    closeTooltip();
  });

  // Gestion du hover sur le tooltip
  tt.addEventListener('mouseenter', () => {
    isMouseOverTooltip = true;
    isTooltipActive = true;
  });

  tt.addEventListener('mouseleave', () => {
    isMouseOverTooltip = false;
    closeTooltip();
  });

  // Fermeture au clic en dehors
  document.addEventListener('click', (e) => {
    if (!tt.contains(e.target) && !badge.contains(e.target)) {
      isTooltipActive = false;
      tt.style.opacity = '0';
      setTimeout(() => {
        tt.remove();
        if (activeTooltip === tt) {
          activeTooltip = null;
        }
      }, 150);
    }
  }, { once: true });
}

// Création et affichage du tooltip
function createTooltip(event, productInfo) {
  // ...existing code...
  
  // Ajout des gestionnaires d'événements pour les actions sur le tooltip
  tooltipElement.addEventListener('click', (event) => {
    // Gestion du bouton "Analyser plus d'avis"
    if (event.target.classList.contains('ari-load-more')) {
      const asin = event.target.getAttribute('data-asin');
      if (asin) {
        event.target.textContent = 'Chargement en cours...';
        event.target.disabled = true;
        
        // Envoyer un message au service worker pour charger plus d'avis
        chrome.runtime.sendMessage({
          action: 'fetchMoreReviews',
          asin: asin,
          url: window.location.href
        }, (response) => {
          if (response && response.success) {
            // Mise à jour du tooltip avec les nouvelles données
            tooltipElement.innerHTML = generateTooltipContent(response.data);
          } else {
            event.target.textContent = 'Échec du chargement';
            setTimeout(() => {
              event.target.textContent = 'Réessayer';
              event.target.disabled = false;
            }, 2000);
          }
        });
      }
    }
  });
  
  // ...existing code...
}

// Configuration des événements sur un badge
function setupBadgeEvents(badge, asin, cachedData = null) {
  ariContentLogger.event('Configuration des événements du badge', { asin });

  const newBadge = badge.cloneNode(true);
  badge.parentNode.replaceChild(newBadge, badge);
  
  newBadge.addEventListener('click', () => {
    ariContentLogger.event('Badge cliqué', { 
      asin, 
      state: newBadge.classList.toString()
    });
    
    if (newBadge.classList.contains('inactive')) {
      // Vérifier si on peut traiter immédiatement ou mettre en file d'attente
      if (analysisQueue.processing.size >= analysisQueue.config.maxParallelAnalyses) {
        // Ajouter à la file d'attente avec priorité élevée (clic utilisateur)
        const added = analysisQueue.add(newBadge, asin, 20);
        
        if (added) {
          // Pas besoin de mettre à jour l'état ici car add() le fait déjà
          showTooltip(newBadge, {
            message: `Ajouté à la file d'attente (${analysisQueue.queue.length} produits en attente)`
          });
        } else if (analysisQueue.isQueued(asin)) {
          // Si déjà dans la file, s'assurer que le badge est bien en état "loading"
          updateBadgeState(newBadge, 'loading');
          showTooltip(newBadge, {
            message: `Déjà dans la file d'attente (${analysisQueue.queue.findIndex(i => i.asin === asin) + 1}/${analysisQueue.queue.length})`
          });
        }
      } else {
        // Démarrer immédiatement
        startAnalysis(newBadge, asin);
      }
    } else if (newBadge.classList.contains('ready')) {
      showTooltip(newBadge, cachedData);
    } else if (newBadge.classList.contains('loading')) {
      // Si le badge est déjà en état "loading", afficher sa position dans la file
      if (analysisQueue.isQueued(asin)) {
        showTooltip(newBadge, {
          message: `En attente d'analyse (${analysisQueue.queue.findIndex(i => i.asin === asin) + 1}/${analysisQueue.queue.length})`
        });
      } else {
        showTooltip(newBadge, {
          message: "Analyse en cours..."
        });
      }
    }
  });

  newBadge.addEventListener('mouseenter', () => {
    ariContentLogger.event('Survol du badge', { 
      asin, 
      state: newBadge.classList.toString()
    });
    
    if (newBadge.classList.contains('ready') && cachedData) {
      showTooltip(newBadge, cachedData);
    } else if (newBadge.classList.contains('inactive')) {
      // Vérifier si en file d'attente
      if (analysisQueue.isQueued(asin)) {
        showTooltip(newBadge, {
          message: `En attente d'analyse (${analysisQueue.queue.findIndex(i => i.asin === asin) + 1}/${analysisQueue.queue.length})`
        });
      } else {
        showTooltip(newBadge, {
          message: "Cliquez pour analyser les avis de ce produit"
        });
      }
    } else if (newBadge.classList.contains('loading')) {
      // Vérifier si l'élément est en file d'attente ou en cours de traitement
      if (analysisQueue.isQueued(asin)) {
        showTooltip(newBadge, {
          message: `En attente d'analyse (${analysisQueue.queue.findIndex(i => i.asin === asin) + 1}/${analysisQueue.queue.length})`
        });
      } else if (analysisQueue.isProcessing(asin)) {
        showTooltip(newBadge, {
          message: "Récupération des avis en cours..."
        });
      } else {
        showTooltip(newBadge, {
          message: "Analyse en préparation..."
        });
      }
    } else if (newBadge.classList.contains('processing')) {
      showTooltip(newBadge, {
        message: "Analyse IA des avis en cours..."
      });
    }
  });

  return newBadge;
}

// Initialisation d'un badge
async function initializeBadge(badge, asin) {
  ariContentLogger.badge('Initialisation du badge', { asin });

  try {
    ariContentLogger.cache('Vérification du cache', { asin });
    const response = await chrome.runtime.sendMessage({
      type: 'get_cache',
      asin: asin
    });

    if (response.cached) {
      ariContentLogger.cache('Données trouvées en cache', { 
        asin,
        pros: response.data.summary?.pros?.length || 0,
        cons: response.data.summary?.cons?.length || 0 
      });
      updateBadgeState(badge, 'ready');
      setupBadgeEvents(badge, asin, response.data);
      return;
    }
    ariContentLogger.cache('Aucune donnée en cache', { asin });
    
    // Vérifier si le mode auto est activé
    if (analysisQueue.config && analysisQueue.config.autoAnalysisEnabled) {
      ariContentLogger.info('Mode automatique activé, démarrage de l\'analyse pour', { asin });
      
      // Ajouter à la file d'attente avec faible priorité (mode auto)
      analysisQueue.add(badge, asin, 5);
    } else {
      ariContentLogger.debug('Badge laissé inactif (mode manuel)', { asin });
    }
  } catch (error) {
    ariContentLogger.error('Échec de la vérification du cache', { 
      asin, 
      error: error.message,
      stack: error.stack 
    });
  }

  setupBadgeEvents(badge, asin);
}

// Fonction améliorée pour le démarrage de l'analyse avec meilleur débogage de la communication avec le service worker
async function startAnalysis(badge, asin) {
  if (analysisQueue.isProcessing(asin)) {
    ariContentLogger.warn('Analyse déjà en cours', { asin });
    return;
  }
  
  try {
    // Mise à jour de l'état : chargement
    updateBadgeState(badge, 'loading');
    showTooltip(badge, {
      message: "Récupération des avis en cours..."
    });
    
    ariContentLogger.event('Démarrage de l\'analyse', { asin });
    
    // Vérifier que le service worker est actif avant de continuer
    try {
      ariContentLogger.debug('Test de connexion avec le service worker', { asin });
      const pingResponse = await chrome.runtime.sendMessage({ type: 'ping' });
      
      if (!pingResponse || pingResponse.error) {
        ariContentLogger.error('Service worker non disponible ou en erreur', { 
          asin, 
          response: pingResponse 
        });
        throw new Error('Service worker non disponible');
      }
      
      ariContentLogger.debug('Service worker disponible', { 
        asin, 
        response: pingResponse
      });
    } catch (pingError) {
      ariContentLogger.error('Erreur lors du ping du service worker', {
        asin,
        error: pingError.message,
        stack: pingError.stack
      });
      // On continue malgré l'erreur pour tenter quand même l'analyse
    }
    
    // Mise à jour de l'état : traitement
    updateBadgeState(badge, 'processing');
    showTooltip(badge, {
      message: "Analyse IA des avis en cours..."
    });
    
    // Envoi de la requête au service worker avec timeout
    ariContentLogger.debug('Envoi du message au service worker', { asin });
    
    // Promesse avec timeout pour éviter les blocages
    const fetchWithTimeout = Promise.race([
      // La promesse de fetch
      chrome.runtime.sendMessage({ 
        type: 'fetch', 
        asin,
        locale: window.location.hostname.endsWith('.fr') ? 'fr' : 'en' 
      }),
      // Une promesse de timeout
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout: Pas de réponse du service worker après 30s')), 30000)
      )
    ]);
    
    const response = await fetchWithTimeout;

    // Vérifier si la réponse contient une erreur
    if (!response) {
      throw new Error('Pas de réponse du service worker');
    }
    
    if (response.error) {
      throw new Error(response.error);
    }

    ariContentLogger.event('Analyse terminée', { 
      asin, 
      prosCount: response.pros?.length || 0,
      consCount: response.cons?.length || 0
    });
    
    // Mise à jour de l'état : prêt
    updateBadgeState(badge, 'ready');
    
    // Configurer les événements avec les résultats de l'analyse, mais ne pas afficher le tooltip automatiquement
    setupBadgeEvents(badge, asin, response);
    
    // Fermer le tooltip actuel s'il existe
    const currentTooltip = document.getElementById(TOOLTIP_ID);
    if (currentTooltip) {
      isTooltipActive = false;
      currentTooltip.style.opacity = '0';
      setTimeout(() => currentTooltip.remove(), 150);
    }

  } catch (error) {
    ariContentLogger.error('Échec de l\'analyse', { 
      asin, 
      error: error.message,
      stack: error.stack 
    });
    
    // Mise à jour de l'état : inactif (erreur)
    updateBadgeState(badge, 'inactive');
    
    // Stocker le message d'erreur mais ne pas afficher automatiquement le tooltip
    const errorData = {
      error: `Erreur: ${error.message}. Cliquez pour réessayer.`
    };
    
    // Configurer les événements pour afficher l'erreur uniquement au survol
    setupBadgeEvents(badge, asin, errorData);
    
    // Fermer le tooltip actuel s'il existe
    const currentTooltip = document.getElementById(TOOLTIP_ID);
    if (currentTooltip) {
      isTooltipActive = false;
      currentTooltip.style.opacity = '0';
      setTimeout(() => currentTooltip.remove(), 150);
    }
  }
}

// Ajout des badges dans la page
function addBadges() {
  // Vérifier si nous sommes en train de gérer un changement de page récent
  const justChangedPage = Date.now() - lastPageProcessed < 1000;
  if (justChangedPage) {
    // Si nous venons de changer de page, s'assurer que le compteur est remis à zéro
    analysisQueue.initialAnalysisCount = 0;
    ariContentLogger.info('Réinitialisation du compteur lors de l\'injection des badges suite à un changement de page', {
      initialAnalysisCount: 0
    });
  }

  ariContentLogger.info('Démarrage de l\'injection des badges', {
    pageUrl: window.location.href,
    initialAnalysisCount: analysisQueue.initialAnalysisCount
  });

  // Map pour suivre les ASINs déjà traités par conteneur
  const processedContainers = new WeakMap();

  // Sélecteurs unifiés pour les boutons d'ajout au panier
  const buttonsQuery = `
    span[data-action="puis-atcb-add-action-retail"],
    .puis-atcb-add-container form,
    form[action*="/cart/add-to-cart"]:not(.ari-processed),
    .ax-atc:not(.ari-processed),
    .puis-desktop-atc-button-container:not(.ari-processed),
    [data-action="ax-add-to-cart"]:not(.ari-processed)
  `;

  // Récupérer tous les boutons d'ajout au panier
  const cartButtons = document.querySelectorAll(buttonsQuery);

  ariContentLogger.info('Boutons de panier trouvés', { count: cartButtons.length });

  cartButtons.forEach(async button => {
    // Marquer le bouton comme traité
    button.classList.add('ari-processed');
    
    // Vérifier si le bouton est déjà dans un wrapper ARI
    if (button.closest('.ari-cart-button-wrapper')) {
      ariContentLogger.debug('Bouton déjà traité, ignoré', {
        buttonHtml: button.outerHTML.substring(0, 50) + '...'
      });
      return;
    }

    // Trouver le conteneur parent avec l'ASIN
    let containerElement = button.closest('[data-asin], [data-csa-c-item-id], [cel_widget_id*="MAIN-SEARCH_RESULTS"], [data-csa-c-content-id*="ax-atc"], [data-csa-c-content-id]');
    
    if (!containerElement) {
      // Recherche étendue dans la hiérarchie des parents
      let parent = button.parentElement;
      for (let i = 0; i < 6 && parent; i++) {
        if (parent.hasAttribute('data-asin') || 
            parent.hasAttribute('data-csa-c-item-id') || 
            (parent.hasAttribute('data-csa-c-content-id') && parent.getAttribute('data-csa-c-content-id').includes('asin'))) {
          containerElement = parent;
          break;
        }
        parent = parent.parentElement;
      }
    }
    
    if (!containerElement) {
      ariContentLogger.warn('Aucun conteneur trouvé pour le bouton', {
        buttonHtml: button.outerHTML.substring(0, 50) + '...'
      });
      return;
    }

    // Vérifier si ce conteneur a déjà été traité
    if (processedContainers.has(containerElement)) {
      ariContentLogger.debug('Conteneur déjà traité, ignoré', {
        container: containerElement.outerHTML.substring(0, 50) + '...'
      });
      return;
    }

    // Marquer le conteneur comme traité
    processedContainers.set(containerElement, true);

    // Obtenir l'ASIN
    let asin = extractAsin(containerElement, button);

    if (!asin) {
      ariContentLogger.error('Aucun ASIN trouvé', { 
        containerHTML: containerElement.outerHTML.substring(0, 100) + '...' 
      });
      return;
    }

    // Vérifier si un badge existe déjà pour cet ASIN dans le conteneur
    const existingBadge = containerElement.querySelector(`.ari-badge-container[data-ari-asin="${asin}"]`);
    if (existingBadge) {
      ariContentLogger.debug('Badge existant trouvé pour cet ASIN', { asin });
      return;
    }

    ariContentLogger.badge('Ajout du badge', { asin });

    // Créer le wrapper
    const wrapper = document.createElement('div');
    wrapper.className = 'ari-cart-button-wrapper';

    // Ajouter le bouton existant au wrapper
    const parent = button.parentNode;
    parent.insertBefore(wrapper, button);
    wrapper.appendChild(button);

    // Ajouter le badge
    const badgeContainer = document.createElement('div');
    badgeContainer.className = 'ari-badge-container';
    badgeContainer.setAttribute('data-ari-asin', asin);
    badgeContainer.innerHTML = BADGE_HTML;
    wrapper.appendChild(badgeContainer);

    // Initialiser le badge
    const badge = badgeContainer.querySelector('.ari-badge');
    await initializeBadge(badge, asin);
  });
}

/**
 * Extrait l'ASIN d'un élément en utilisant plusieurs méthodes
 * @param {HTMLElement} containerElement - Conteneur potentiellement avec l'ASIN
 * @param {HTMLElement} button - Bouton d'ajout au panier
 * @returns {string|null} - L'ASIN ou null si non trouvé
 */
function extractAsin(containerElement, button) {
  const methods = {
    // Méthode prioritaire : attribut data-asin direct
    fromDataAsin: (el) => el.getAttribute('data-asin'),
    
    // Depuis le conteneur du produit
    fromListItem: (el) => el.closest('[role="listitem"][data-asin]')?.getAttribute('data-asin'),
    
    // Format amzn1.asin dans data-csa-c-item-id
    fromCsaItemId: (el) => {
      const itemId = el.getAttribute('data-csa-c-item-id') || '';
      const match = itemId.match(/amzn1\.asin\.1\.([A-Z0-9]{10})/);
      return match?.[1];
    },
    
    // Depuis les formulaires d'ajout au panier
    fromCartForm: (el) => {
      const form = el.closest('form[action*="/cart/add-to-cart"]');
      if (!form) return null;
      
      // Vérifier les inputs cachés
      const hiddenInput = form.querySelector('input[name*="asin"][type="hidden"]');
      if (hiddenInput?.value) return hiddenInput.value;
      
      // Vérifier l'URL du formulaire
      const formAction = form.getAttribute('action') || '';
      const urlMatch = formAction.match(/\/dp\/([A-Z0-9]{10})/);
      return urlMatch?.[1];
    },
    
    // Depuis les attributs de données spécifiques à Amazon
    fromAmazonData: (el) => {
      const selectors = [
        '[data-asin]',
        '[data-component-id*="sp_detail"]',
        '[data-component-type="s-search-result"]',
        '.s-asin',
        '.s-result-item'
      ];
      
      for (const selector of selectors) {
        const element = el.closest(selector);
        if (element?.dataset?.asin) return element.dataset.asin;
      }
      return null;
    },
    
    // Depuis les liens du produit
    fromProductLinks: (el) => {
      const links = el.querySelectorAll('a[href*="/dp/"], a[href*="/product/"]');
      for (const link of links) {
        const href = link.getAttribute('href') || '';
        const match = href.match(/\/(?:dp|product)\/([A-Z0-9]{10})/);
        if (match?.[1]) return match[1];
      }
      return null;
    }
  };

  // Essayer toutes les méthodes dans l'ordre de fiabilité
  for (const methodName of Object.keys(methods)) {
    try {
      // Essayer sur le conteneur
      const asin = methods[methodName](containerElement);
      if (asin?.match(/^[A-Z0-9]{10}$/)) {
        ariContentLogger.debug(`ASIN trouvé via ${methodName}`, { asin });
        return asin;
      }
      
      // Si pas trouvé, essayer sur le bouton
      const buttonAsin = methods[methodName](button);
      if (buttonAsin?.match(/^[A-Z0-9]{10}$/)) {
        ariContentLogger.debug(`ASIN trouvé via ${methodName} (bouton)`, { asin: buttonAsin });
        return buttonAsin;
      }
    } catch (error) {
      ariContentLogger.error(`Erreur avec la méthode ${methodName}`, { 
        error: error.message,
        containerHTML: containerElement.outerHTML.substring(0, 100) + '...'
      });
    }
  }

  // Dernière tentative : chercher un ASIN dans tous les attributs data-*
  try {
    const allDataAttributes = [...containerElement.attributes]
      .filter(attr => attr.name.startsWith('data-'))
      .map(attr => attr.value);
      
    for (const attrValue of allDataAttributes) {
      const match = attrValue.match(/[A-Z0-9]{10}/);
      if (match && match[0].length === 10) {
        ariContentLogger.debug('ASIN trouvé dans un attribut data-*', { 
          asin: match[0],
          attribute: attrValue
        });
        return match[0];
      }
    }
  } catch (error) {
    ariContentLogger.error('Erreur lors de la recherche dans les attributs data-*', {
      error: error.message
    });
  }

  ariContentLogger.error('Aucun ASIN trouvé', {
    containerHTML: containerElement.outerHTML.substring(0, 100) + '...'
  });
  return null;
}

// Éviter les boucles infinies avec le MutationObserver
let observerTimeout = null;

const observer = new MutationObserver((mutations) => {
  // Ignorer les mutations sur nos propres éléments
  if (mutations.some(m => 
    m.target.classList?.contains('ari-cart-button-wrapper') || 
    m.target.classList?.contains('ari-badge-container') ||
    m.target.id === TOOLTIP_ID)) {
    return;
  }

  clearTimeout(observerTimeout);
  observerTimeout = setTimeout(() => {
    // Vérifier s'il y a eu un changement significatif de page
    const isPageChange = detectPageChange();
    
    if (isPageChange) {
      // Réinitialiser la file d'attente d'analysis pour la nouvelle page
      if (analysisQueue) {
        analysisQueue.clear();
        ariContentLogger.info('File d\'attente vidée suite à un changement de page');
      }
    }
    
    ariContentLogger.debug('Mutations DOM détectées, mise à jour des badges', { 
      isPageChange
    });
    
    // Ne pas supprimer le tooltip actif si l'utilisateur est en train de l'utiliser
    if (!isTooltipActive) {
      const tooltips = document.querySelectorAll(`#${TOOLTIP_ID}`);
      tooltips.forEach(t => {
        // Vérifier si le tooltip n'est pas celui avec lequel l'utilisateur interagit
        if (t !== activeTooltip) {
          t.remove();
        }
      });
    }
    
    addBadges();
  }, 500);
});

// Surveillance des changements d'URL pour les sites qui utilisent l'API History
let lastUrl = location.href;
function setupUrlChangeDetection() {
  // Observer les changements d'URL sans rechargement de page (History API)
  const observer = new MutationObserver(() => {
    if (location.href !== lastUrl) {
      ariContentLogger.info("Changement d'URL détecté via History API", {
        from: lastUrl,
        to: location.href
      });
      
      // Réinitialiser les variables de la page
      lastUrl = location.href;
      
      // Forcer un changement de page complet
      handlePageChange(true);
    }
  });
  
  // Observer les changements sur le document entier
  observer.observe(document, { subtree: true, childList: true });
  
  // Surveiller également les événements popstate (navigation avant/arrière)
  window.addEventListener('popstate', () => {
    if (location.href !== lastUrl) {
      ariContentLogger.info("Changement d'URL détecté via popstate", {
        from: lastUrl,
        to: location.href
      });
      
      lastUrl = location.href;
      handlePageChange(true);
    }
  });
  
  // Surveiller les événements hashchange également
  window.addEventListener('hashchange', () => {
    ariContentLogger.info('Changement de hash détecté', {
      from: lastUrl,
      to: location.href
    });
    
    lastUrl = location.href;
    handlePageChange(true);
  });
}

// Fonction centralisée pour gérer les changements de page
function handlePageChange(isUrlChange = false) {
  // Mettre à jour l'URL courante et le timestamp
  if (!isUrlChange) {
    currentPageUrl = window.location.href;
  }
  lastPageProcessed = Date.now();
  
  // Réinitialiser le compteur d'analyses initiales
  if (analysisQueue) {
    // Compteur remis à zéro pour permettre de nouvelles analyses initiales
    analysisQueue.initialAnalysisCount = 0;
    
    // Vider la file d'attente d'analyses
    analysisQueue.queue = [];
    
    // Nettoyer également la liste des analyses en cours pour éviter les blocages
    // Attention : cela pourrait interrompre des analyses en cours
    if (analysisQueue.processing.size > 0) {
      ariContentLogger.warn('Nettoyage des analyses en cours suite à un changement de page', {
        processingCount: analysisQueue.processing.size
      });
      analysisQueue.processing.clear();
    }
    
    ariContentLogger.info('Réinitialisation complète suite à un changement de page', {
      isUrlChange,
      initialAnalysisCount: 0,
      queueLength: 0
    });
  }
  
  // Nettoyer tous les tooltips
  const tooltips = document.querySelectorAll(`#${TOOLTIP_ID}`);
  tooltips.forEach(t => t.remove());
  activeTooltip = null;
  isTooltipActive = false;
  
  return true;
}

// Gestionnaire de messages pour les mises à jour de configuration
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'config_updated') {
    ariContentLogger.info('Message de mise à jour de configuration reçu');
    
    // Recharger la configuration en respectant le paramètre du mode automatique
    analysisQueue.loadConfig().then(() => {
      ariContentLogger.info('Configuration rechargée', {
        autoAnalysisEnabled: analysisQueue.config.autoAnalysisEnabled,
        maxParallelAnalyses: analysisQueue.config.maxParallelAnalyses
      });
      
      // Mise à jour de l'état d'exécution du mode auto selon la configuration
      analysisQueue.isAutoAnalysisRunning = analysisQueue.config.autoAnalysisEnabled;
      
      // Re-scanner la page pour appliquer le mode automatique aux nouveaux badges si activé
      if (analysisQueue.config.autoAnalysisEnabled) {
        setTimeout(() => {
          addBadges();
        }, 500);
      }
    });
    
    // Répondre pour confirmer la réception
    if (sendResponse) {
      sendResponse({ success: true });
    }
  }
});

// Variables globales pour la détection de changement de page
// Note: currentPageUrl et lastPageProcessed sont déjà déclarés en haut du fichier
// et sont utilisés à la fois ici et dans handlePageChange()

// Fonction pour détecter un changement significatif de page (comme une nouvelle recherche)
function detectPageChange() {
  try {
    // Si l'URL a changé, c'est clairement une nouvelle page
    const urlChanged = currentPageUrl !== window.location.href;
    
    // Même si l'URL n'a pas changé (par exemple navigation Ajax),
    // si beaucoup de temps s'est écoulé depuis le dernier traitement, 
    // on considère que c'est un changement significatif
    const timeSinceLastProcess = Date.now() - lastPageProcessed;
    const significantTimeElapsed = timeSinceLastProcess > 2000; // 2 secondes
    
    if (urlChanged || significantTimeElapsed) {
      try {
        ariContentLogger.info('Changement de page détecté', {
          urlChanged,
          from: currentPageUrl,
          to: window.location.href,
          timeSinceLastProcess: timeSinceLastProcess
        });
      } catch (logError) {
        // Si le logger provoque une erreur, utiliser la console standard
        console.log('ARI: Changement de page détecté', {
          urlChanged,
          from: currentPageUrl,
          to: window.location.href,
          timeSinceLastProcess: timeSinceLastProcess
        });
      }
      
      // Utiliser la fonction centralisée pour gérer le changement de page
      return handlePageChange(false);
    }
    
    return false;
  } catch (error) {
    // En cas d'erreur (par exemple contexte invalide), essayer de loguer mais ne pas faire planter la page
    try {
      console.warn('Erreur lors de la détection de changement de page', error);
    } catch (e) {
      // Dernière tentative silencieuse
    }
    return false;
  }
}

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
  ensureLoggerInitialized().then(() => {
    // Configurer la détection de changement d'URL avant tout
    setupUrlChangeDetection();
    
    addBadges();
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });
    
    ariContentLogger.info('Observation du DOM démarrée');
    
    // Attendre un instant pour que les badges soient ajoutés avant de charger la config
    setTimeout(() => {
      analysisQueue.loadConfig();
    }, 1000);
  });
});

// Initialiser immédiatement si le DOM est déjà chargé
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  ensureLoggerInitialized().then(() => {
    // Configurer la détection de changement d'URL avant tout
    setupUrlChangeDetection();
    
    addBadges();
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });
    
    ariContentLogger.info('Observation du DOM démarrée (load immédiat)');
    
    // Attendre un instant pour que les badges soient ajoutés avant de charger la config
    setTimeout(() => {
      analysisQueue.loadConfig();
    }, 1000);
  });
}

// Gestionnaire d'événements pour le tooltip
function setupTooltipEvents() {
  ariContentLogger.debug('Configuration des événements du tooltip');
  
  // Délégation d'événements pour gérer les clics à l'intérieur du tooltip
  document.getElementById('ari-tooltip').addEventListener('click', function(e) {
    // Gestion du bouton "Analyser plus d'avis"
    if (e.target.classList.contains('ari-load-more')) {
      const asin = e.target.getAttribute('data-asin');
      e.target.textContent = 'Chargement...';
      e.target.disabled = true;
      
      // Envoyer un message au service worker pour charger plus d'avis
      chrome.runtime.sendMessage({
        action: 'fetchMoreReviews',
        asin: asin
      }, function(response) {
        if (response && response.success) {
          // Mettre à jour le tooltip avec les nouveaux résultats
          updateTooltipContent(response.data);
          ariContentLogger.debug('Avis supplémentaires chargés avec succès');
        } else {
          e.target.textContent = 'Analyser plus d\'avis';
          e.target.disabled = false;
          ariContentLogger.error('Erreur lors du chargement des avis supplémentaires', response);
        }
      });
    }
  });
}

// Mise à jour du contenu du tooltip avec de nouvelles données
function updateTooltipContent(data) {
  const tooltipElement = document.getElementById('ari-tooltip');
  if (tooltipElement) {
    tooltipElement.innerHTML = generateTooltipContent(data);
  }
}

// Ajouter un gestionnaire d'événements pour le bouton "Analyser plus d'avis"
document.addEventListener('click', function(event) {
  if (event.target.classList.contains('ari-load-more')) {
    const asin = event.target.getAttribute('data-asin');
    if (asin) {
      // Afficher un indicateur de chargement
      event.target.textContent = 'Chargement...';
      event.target.disabled = true;
      
      // Envoyer un message au service worker pour charger plus d'avis
      chrome.runtime.sendMessage({
        action: 'fetchMoreReviews',
        asin: asin
      }, function(response) {
        if (response && response.success) {
          // Mettre à jour le tooltip avec les nouvelles données
          updateTooltipContent(response.data);
          
          // Si tous les avis ont été chargés, masquer le bouton
          if (!response.data.hasMoreReviews) {
            const loadMoreButton = document.querySelector('.ari-load-more');
            if (loadMoreButton) {
              loadMoreButton.style.display = 'none';
            }
          } else {
            // Réinitialiser le bouton
            event.target.textContent = 'Analyser plus d\'avis';
            event.target.disabled = false;
          }
        } else {
          // Gérer les erreurs
          console.error('Erreur lors du chargement d\'avis supplémentaires');
          event.target.textContent = 'Erreur - Réessayer';
          event.target.disabled = false;
        }
      });
    }
  }
});