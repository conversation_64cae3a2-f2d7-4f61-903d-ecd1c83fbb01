/* Conteneurs */
.ari-cart-button-wrapper {
  display: inline-flex !important;
  align-items: center !important;
  gap: 4px !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ari-badge-container {
  display: inline-flex !important;
  align-items: center !important;
  margin-left: 4px !important;
  vertical-align: middle !important;
  position: relative !important;
}

/* Style du badge adapté au design Amazon */
.ari-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 36px !important;
  height: 29px !important;
  border-radius: 3px !important;
  font-size: 13px !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-family: "Amazon Ember", Arial, sans-serif !important;
  padding: 0 8px !important;
  border: 1px solid !important;
  box-shadow: 0 1px 0 rgba(255,255,255,.4) inset !important;
  text-decoration: none !important;
  vertical-align: middle !important;
  line-height: 29px !important;
}

/* États des badges */
.ari-badge.inactive {
  background: linear-gradient(to bottom, #f4d4d4 0%, #f0877b 100%) !important;
  border-color: #c94137 !important;
  color: #111 !important;
}

.ari-badge.loading {
  animation: pulse 1.5s ease-in-out infinite !important;
  background: linear-gradient(to bottom, #feedd4 0%, #f4c78e 100%) !important;
  border-color: #e68a00 !important;
  color: #111 !important;
}

.ari-badge.processing {
  background: linear-gradient(to bottom, #d4e9fa 0%, #8acdff 100%) !important;
  border-color: #1976d2 !important;
  color: #111 !important;
}

.ari-badge.ready {
  background: linear-gradient(to bottom, #dcf5dc 0%, #7cc47c 100%) !important;
  border-color: #2b8a2b !important;
  color: #111 !important;
}

.ari-badge.disabled {
  background: linear-gradient(to bottom, #f7f7f7 0%, #e6e6e6 100%) !important;
  border-color: #adb1b8 #a2a6ac #8d9096 !important;
  color: #666 !important;
  opacity: 0.8 !important;
  cursor: not-allowed !important;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Tooltip style Amazon - Correction pour affichage correct */
#ari-insights-tooltip {
  position: absolute !important;
  z-index: 2147483647 !important;
  min-width: 280px !important;
  max-width: 320px !important;
  background: #fff !important;
  color: #111 !important;
  border-radius: 3px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,.13) !important;
  padding: 14px !important;
  font-size: 13px !important;
  line-height: 19px !important;
  font-family: "Amazon Ember", Arial, sans-serif !important;
  border: 1px solid #cdcdcd !important;
  animation: ari-fade-in 0.15s ease-out !important;
  opacity: 1 !important;
  transition: opacity 0.15s ease-out !important;
  z-index: 2147483647 !important; /* S'assurer que le tooltip est toujours au-dessus */
  pointer-events: auto !important; /* Permettre l'interaction avec le tooltip */
}

/* Styles pour le contenu interne du tooltip */
.ari-tooltip-content {
  width: 100% !important;
}

.ari-header {
  font-weight: 700 !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  padding-bottom: 4px !important;
  border-bottom: 1px solid #eee !important;
  color: #0F1111 !important;
}

.ari-stats {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 10px !important;
  font-size: 12px !important;
}

.ari-stat {
  display: flex !important;
  gap: 4px !important;
}

.ari-stat-label {
  color: #555 !important;
}

.ari-stat-value {
  font-weight: 600 !important;
}

.ari-sentiment {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 10px !important;
  padding: 4px 8px !important;
  background-color: #f7f7f7 !important;
  border-radius: 3px !important;
}

.ari-sentiment-label {
  margin-right: 8px !important;
  font-weight: 600 !important;
}

.ari-sentiment-value {
  padding: 2px 6px !important;
  border-radius: 2px !important;
  font-weight: 600 !important;
}

.ari-sentiment-value.positive {
  background-color: #e6f3e6 !important;
  color: #2b8a2b !important;
}

.ari-sentiment-value.negative {
  background-color: #f8e6e6 !important;
  color: #c94137 !important;
}

.ari-sentiment-value.neutral,
.ari-sentiment-value.mixed {
  background-color: #f0f0f0 !important;
  color: #555 !important;
}

.ari-highlights {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.ari-pros h3, .ari-cons h3 {
  margin: 0 0 6px 0 !important;
  font-size: 13px !important;
  font-weight: 600 !important;
}

.ari-pros h3 {
  color: #2b8a2b !important;
}

.ari-cons h3 {
  color: #c94137 !important;
}

.ari-highlights ul {
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

.ari-highlights li {
  position: relative !important;
  padding-left: 20px !important;
  margin-bottom: 4px !important;
  font-size: 12px !important;
  line-height: 16px !important;
}

.ari-pros li:before {
  content: "✓" !important;
  position: absolute !important;
  left: 4px !important;
  color: #2b8a2b !important;
  font-weight: bold !important;
}

.ari-cons li:before {
  content: "✗" !important;
  position: absolute !important;
  left: 4px !important;
  color: #c94137 !important;
  font-weight: bold !important;
}

.ari-review-count {
  margin-top: 12px !important;
  font-size: 11px !important;
  color: #666 !important;
  text-align: center !important;
}

/* Correction pour la flèche du tooltip qui pointe vers le badge */
#ari-insights-tooltip::before,
#ari-insights-tooltip::after {
  content: '' !important;
  position: absolute !important;
  left: 16px !important;
  top: -9px !important;
  border: solid transparent !important;
  pointer-events: none !important;
  width: 0 !important;
  height: 0 !important;
}

#ari-insights-tooltip::before {
  border-width: 10px !important;
  margin-top: -1px !important;
  border-bottom-color: #cdcdcd !important;
}

#ari-insights-tooltip::after {
  border-width: 9px !important;
  border-bottom-color: #fff !important;
}

#ari-insights-tooltip strong {
  display: block !important;
  color: #0F1111 !important;
  margin: 0 0 6px 0 !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  border-bottom: 1px solid #eee !important;
  padding-bottom: 4px !important;
}

#ari-insights-tooltip ul {
  margin: 0 0 12px 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

#ari-insights-tooltip li {
  margin: 4px 0 !important;
  padding-left: 20px !important;
  position: relative !important;
  line-height: 19px !important;
  color: #333 !important;
}

#ari-insights-tooltip li:before {
  content: "•" !important;
  position: absolute !important;
  left: 6px !important;
  font-size: 16px !important;
}

#ari-insights-tooltip li.pro:before {
  color: #2b8a2b !important;
}

#ari-insights-tooltip li.con:before {
  color: #c94137 !important;
}

#ari-insights-tooltip em {
  display: inline-block !important;
  background: #f8f8f8 !important;
  color: #555 !important;
  font-style: normal !important;
  font-size: 11px !important;
  padding: 0 4px !important;
  border-radius: 2px !important;
  margin-left: 4px !important;
  border: 1px solid #e7e7e7 !important;
}

#ari-insights-tooltip .message {
  margin: 8px 0 !important;
  color: #333 !important;
  font-size: 13px !important;
}

#ari-insights-tooltip .footer {
  margin-top: 10px !important;
  padding-top: 8px !important;
  border-top: 1px solid #eee !important;
  color: #666 !important;
  font-size: 11px !important;
  text-align: center !important;
}

/* Fix pour les boutons Amazon */
.ari-cart-button-wrapper > form,
.ari-cart-button-wrapper > span,
.ari-cart-button-wrapper > div {
  margin: 0 !important;
}

/* Styles pour le bouton "Analyser plus d'avis" */
.ari-load-more-container {
  margin-top: 12px !important;
  text-align: center !important;
  padding-top: 8px !important;
  border-top: 1px solid #eee !important;
}

.ari-load-more {
  background: linear-gradient(to bottom, #f7f8fa, #e7e9ec) !important;
  border-radius: 3px !important;
  border-color: #adb1b8 #a2a6ac #8d9096 !important;
  border-style: solid !important;
  border-width: 1px !important;
  cursor: pointer !important;
  padding: 6px 12px !important;
  font-size: 13px !important;
  font-family: "Amazon Ember", Arial, sans-serif !important;
  display: inline-block !important;
  text-align: center !important;
  text-decoration: none !important;
  vertical-align: middle !important;
  color: #111 !important;
  box-shadow: 0 1px 0 rgba(255,255,255,.4) inset !important;
  transition: all 0.1s linear !important;
}

.ari-load-more:hover {
  background: linear-gradient(to bottom, #e7e9ec, #dcdfe3) !important;
}

.ari-load-more:disabled {
  cursor: not-allowed !important;
  opacity: 0.8 !important;
  background: #e7e9ec !important;
}

/* Mode sombre pour le bouton */
@media (prefers-color-scheme: dark) {
  .ari-load-more-container {
    border-top-color: #2d2d2d !important;
  }
  
  .ari-load-more {
    background: linear-gradient(to bottom, #2d2d2d, #1a1a1a) !important;
    border-color: #444 #333 #2a2a2a !important;
    color: #e6e6e6 !important;
    box-shadow: 0 1px 0 rgba(255,255,255,.1) inset !important;
  }
  
  .ari-load-more:hover {
    background: linear-gradient(to bottom, #333, #222) !important;
  }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  #ari-insights-tooltip {
    background: #1a1a1a !important;
    color: #e6e6e6 !important;
    border-color: #2d2d2d !important;
    box-shadow: 0 2px 4px rgba(0,0,0,.2) !important;
  }

  #ari-insights-tooltip::after {
    border-bottom-color: #1a1a1a !important;
  }

  #ari-insights-tooltip::before {
    border-bottom-color: #2d2d2d !important;
  }

  #ari-insights-tooltip strong {
    color: #fff !important;
    border-bottom-color: #2d2d2d !important;
  }

  #ari-insights-tooltip li {
    color: #ccc !important;
  }

  #ari-insights-tooltip em {
    background: #2d2d2d !important;
    color: #aaa !important;
    border-color: #404040 !important;
  }

  #ari-insights-tooltip .message {
    color: #ccc !important;
  }

  #ari-insights-tooltip .footer {
    border-top-color: #2d2d2d !important;
    color: #999 !important;
  }
}

/* Animation d'apparition */
@keyframes ari-fade-in {
  from { 
    opacity: 0;
    transform: translateY(-4px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}