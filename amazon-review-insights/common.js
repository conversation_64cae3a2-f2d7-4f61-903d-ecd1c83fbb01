/**
 * Système de logs centralisé pour Amazon Review Insights
 * 
 * Niveaux de log:
 * 0 = Désactivé
 * 1 = Erreurs uniquement (production)
 * 2 = Erreurs + Avertissements (par défaut)
 * 3 = Information et opérations normales 
 * 4 = Détails et débogage avancé
 * 5 = Traces complètes (très verbeux)
 */

// Configuration globale du logger
const LOG_LEVELS = {
  ERROR: 1,
  WARN: 2,
  INFO: 3,
  DEBUG: 4
};

// État initial des logs
let logConfig = {
  logLevel: LOG_LEVELS.DEBUG,  // Niveau par défaut
  logToConsole: true,         // Toujours activer les logs console
  logToStorage: true          // Garder une trace dans le stockage
};

// Utilitaire de mesure du temps
async function measure(label, fn) {
  console.time(label);
  try {
    return await fn();
  } finally {
    console.timeEnd(label);
  }
}

// Créer une instance du logger
const logger = {
  error: (msg, data = {}, level = 1) => log('ERROR', msg, data, level),
  warn: (msg, data = {}, level = 2) => log('WARN', msg, data, level),
  info: (msg, data = {}, level = 3) => log('INFO', msg, data, level),
  debug: (msg, data = {}, level = 4) => log('DEBUG', msg, data, level),
  badge: (msg, data = {}, level = 3) => log('BADGE', msg, data, level),
  cache: (msg, data = {}, level = 4) => log('CACHE', msg, data, level),
  event: (msg, data = {}, level = 3) => log('EVENT', msg, data, level)
};

// Fonction principale de log
function log(type, msg, data = {}, level) {
  // Toujours logger les erreurs
  if (type === 'ERROR' || logConfig.logLevel >= level) {
    const logData = {
      timestamp: new Date().toISOString(),
      type,
      message: msg,
      ...data
    };

    // Forcer l'affichage dans la console
    const consoleMsg = `${logData.timestamp} [${type}] ${msg}`;
    switch (type) {
      case 'ERROR':
        console.error(consoleMsg, data);
        break;
      case 'WARN':
        console.warn(consoleMsg, data);
        break;
      case 'DEBUG':
        console.debug(consoleMsg, data);
        break;
      default:
        console.log(consoleMsg, data);
    }

    // Stocker dans chrome.storage si activé
    if (logConfig.logToStorage && chrome?.storage) {
      try {
        chrome.storage.local.get(['logs'], (result) => {
          try {
            const logs = result.logs || [];
            logs.push(logData);
            // Garder seulement les 1000 derniers logs
            if (logs.length > 1000) logs.shift();
            chrome.storage.local.set({ logs });
          } catch (innerError) {
            // Ne pas relancer l'erreur pour éviter les boucles infinies
            console.warn('Impossible de sauvegarder le log dans le stockage', innerError);
          }
        });
      } catch (error) {
        // Contexte d'extension probablement invalidé
        console.warn('Contexte d\'extension probablement invalidé, impossible d\'accéder au stockage', error);
        // Désactiver le stockage des logs pour éviter de futures erreurs
        logConfig.logToStorage = false;
      }
    }
  }
}

// Sauvegarder la configuration
async function saveLogConfig(newConfig) {
  logConfig = { ...logConfig, ...newConfig };
  if (chrome?.storage) {
    await chrome.storage.local.set({ logConfig });
  }
  logger.info('Configuration des logs mise à jour', newConfig);
}

// Récupérer la configuration
async function getLogConfig() {
  if (chrome?.storage) {
    const result = await chrome.storage.local.get(['logConfig']);
    if (result.logConfig) {
      logConfig = result.logConfig;
    }
  }
  return logConfig;
}

// Récupérer tous les logs stockés
async function getLogs() {
  if (chrome?.storage) {
    const result = await chrome.storage.local.get(['logs']);
    return { entries: result.logs || [] };
  }
  return { entries: [] };
}

// Effacer tous les logs stockés
async function clearLogs() {
  if (chrome?.storage) {
    await chrome.storage.local.set({ logs: [] });
    logger.info('Logs effacés');
    return { success: true };
  }
  return { success: false, error: 'Storage non disponible' };
}

// Exporter les fonctionnalités
self.ariLogger = {
  logger,
  measure,
  saveLogConfig,
  getLogConfig,
  getLogs,
  clearLogs,
  LOG_LEVELS
};