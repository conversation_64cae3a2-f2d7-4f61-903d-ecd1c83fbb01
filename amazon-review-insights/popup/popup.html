<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Amazon Review Insights</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      width: 380px;
      margin: 0;
      padding: 0;
      color: #333;
    }
    
    header {
      background-color: #232F3E;
      color: white;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }
    
    .logo {
      height: 24px;
      margin-right: 8px;
    }
    
    .version {
      font-size: 12px;
      opacity: 0.8;
    }
    
    .mode-banner {
      display: flex;
      padding: 10px 16px;
      border-left: 4px solid #1a73e8;
      background-color: #e8f0fe;
      margin-bottom: 12px;
      align-items: center;
    }
    
    .mode-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #1a73e8;
      color: white;
      font-weight: bold;
      margin-right: 12px;
      flex-shrink: 0;
    }
    
    .mode-info h3 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 500;
    }
    
    .mode-info p {
      margin: 0;
      font-size: 12px;
      line-height: 1.4;
    }
    
    .section {
      padding: 16px;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0 0 12px 0;
    }
    
    .form-group {
      margin-bottom: 12px;
    }
    
    label {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-bottom: 4px;
    }
    
    .switch {
      position: relative;
      display: inline-block;
      width: 48px;
      height: 24px;
      margin-right: 8px;
    }
    
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .slider {
      background-color: #4CAF50;
    }
    
    input:checked + .slider:before {
      transform: translateX(24px);
    }
    
    select, input[type="number"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .range-slider {
      width: 100%;
    }

    .range-value {
      text-align: center;
      margin-top: 5px;
      font-size: 14px;
    }
    
    .buttons {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      padding: 16px;
    }
    
    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    .button-primary {
      background-color: #1a73e8;
      color: white;
    }
    
    .button-secondary {
      background-color: transparent;
      color: #1a73e8;
    }
    
    .button-danger {
      background-color: transparent;
      color: #ea4335;
    }
    
    button:hover {
      opacity: 0.9;
    }
    
    .logs-container {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px;
      font-size: 12px;
      font-family: monospace;
      background-color: #f5f5f5;
    }
    
    .log-entry {
      padding: 4px 0;
      border-bottom: 1px solid #eee;
    }
    
    .log-entry:last-child {
      border-bottom: none;
    }
    
    .log-entry-error {
      color: #d32f2f;
    }
    
    .log-entry-warn {
      color: #f57c00;
    }
    
    .badge {
      display: inline-block;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      font-size: 10px;
      line-height: 16px;
      text-align: center;
      margin-right: 4px;
      color: white;
    }
    
    .badge-level-1 {
      background-color: #d32f2f;
    }
    
    .badge-level-2 {
      background-color: #f57c00;
    }
    
    .badge-level-3 {
      background-color: #1976d2;
    }
    
    .badge-level-4 {
      background-color: #388e3c;
    }
    
    .badge-level-5 {
      background-color: #7b1fa2;
    }
    
    .operation-mode-toggle {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }
    
    .operation-mode-toggle .mode-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-left: 8px;
    }
    
    .mode-description {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }

    /* Onglets pour la configuration */
    .tab-buttons {
      display: flex;
      border-bottom: 1px solid #ddd;
      margin-bottom: 15px;
    }

    .tab-button {
      padding: 8px 16px;
      background-color: transparent;
      border: none;
      border-bottom: 2px solid transparent;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }

    .tab-button.active {
      border-bottom: 2px solid #1a73e8;
      color: #1a73e8;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <header>
    <div style="display: flex; align-items: center;">
      <img src="../icons/48.png" alt="Logo" class="logo">
      <h1>Amazon Review Insights</h1>
    </div>
    <span class="version">v0.2.1</span>
  </header>
  
  <div id="mode-banner" class="mode-banner">
    <div id="mode-icon" class="mode-icon">M</div>
    <div class="mode-info">
      <h3 id="mode-title">Mode Manuel Uniquement</h3>
      <p>Pour analyser un produit, vous êtes actuellement en <strong id="current-mode">mode manuel</strong>. <span id="mode-description">Cherchez le badge <strong>R</strong> à côté des étoiles sur les pages Amazon et cliquez dessus.</span></p>
    </div>
  </div>
  
  <div class="section">
    <div class="tab-buttons">
      <button class="tab-button active" data-tab="general">Général</button>
      <button class="tab-button" data-tab="market">Analyse de marché</button>
      <button class="tab-button" data-tab="logs">Logs</button>
    </div>
    
    <!-- Onglet Général -->
    <div class="tab-content active" id="general-tab">
      <h2 class="section-title">Mode de fonctionnement</h2>
      <div class="operation-mode-toggle">
        <label class="switch">
          <input type="checkbox" id="operationMode">
          <span class="slider"></span>
        </label>
        <div class="mode-label">
          <strong>Mode Automatique</strong>
          <div class="mode-description">Activez pour analyser automatiquement les produits lors de la navigation</div>
        </div>
      </div>
      
      <h2 class="section-title">Configuration générale</h2>
      
      <div class="form-group">
        <label for="cacheDurationSelect">Durée de cache des analyses</label>
        <select id="cacheDurationSelect">
          <option value="1_jour">1 jour</option>
          <option value="1_semaine">1 semaine</option>
          <option value="1_mois">1 mois</option>
          <option value="6_mois">6 mois</option>
        </select>
        <div class="mode-description">Cette durée s'applique également aux données de marché</div>
      </div>
      
      <div class="form-group">
        <label for="maxParallelAnalyses">Analyses parallèles maximales</label>
        <input type="number" id="maxParallelAnalyses" min="1" max="10" value="5">
      </div>
      
      <div id="autoAnalysisOptions" style="display: none;">
        <div class="form-group">
          <label for="autoAnalysisEnabled">
            <input type="checkbox" id="autoAnalysisEnabled">
            Activer l'analyse automatique
          </label>
        </div>
      </div>
    </div>
    
    <!-- Onglet Analyse de marché -->
    <div class="tab-content" id="market-tab">
      <h2 class="section-title">Analyse de marché</h2>
      
      <div class="form-group">
        <label for="reviewsPerProductSlider">Avis par produit</label>
        <input type="range" id="reviewsPerProductSlider" class="range-slider" min="120" max="600" step="60" value="300">
        <div id="reviewsPerProductValue" class="range-value">300 avis</div>
      </div>
      
      <div class="form-group">
        <label for="marketParallelFetchesSlider">Requêtes parallèles</label>
        <input type="range" id="marketParallelFetchesSlider" class="range-slider" min="1" max="5" step="1" value="3">
        <div id="marketParallelFetchesValue" class="range-value">3 requêtes en parallèle</div>
        <div class="mode-description">Recommandé: 3 pour éviter d'être bloqué par Amazon</div>
      </div>
      
      <div class="form-group">
        <label>
          <input type="checkbox" id="marketAutoAnalysis">
          Lancer automatiquement l'analyse sur les pages de recherche
        </label>
        <div class="mode-description">Attention: peut consommer beaucoup de ressources</div>
      </div>
      
      <div class="form-group">
        <label for="defaultVisualization">Visualisation par défaut</label>
        <select id="defaultVisualization">
          <option value="scatter">Graphique prix/notation</option>
          <option value="specs">Tableau comparatif</option>
          <option value="sentiment">Analyse de sentiment</option>
          <option value="table">Tableau simple</option>
        </select>
      </div>
    </div>
    
    <!-- Onglet Logs -->
    <div class="tab-content" id="logs-tab">
      <h2 class="section-title">Configuration des logs</h2>
      
      <div class="form-group">
        <label for="logLevel">Niveau de log</label>
        <select id="logLevel">
          <option value="1">1 - Erreurs uniquement</option>
          <option value="2">2 - Erreurs + Avertissements</option>
          <option value="3">3 - Erreurs + Avertissements + Info</option>
          <option value="4">4 - Erreurs + Avertissements + Info + Debug</option>
          <option value="5">5 - Tout (Trace)</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>
          <input type="checkbox" id="logToConsole">
          Enregistrer les logs dans la console
        </label>
      </div>
      
      <div class="form-group">
        <label>
          <input type="checkbox" id="logToStorage">
          Enregistrer les logs dans le stockage local
        </label>
      </div>
      
      <div class="form-group">
        <label for="maxStoredLogs">Nombre maximum de logs stockés</label>
        <input type="number" id="maxStoredLogs" min="100" max="10000" value="1000">
      </div>
      
      <h2 class="section-title">Logs enregistrés <span id="logCount">0</span></h2>
      <p id="logStats">Chargement des statistiques...</p>
      <div id="logsContainer" class="logs-container">
        <div class="log-entry">Chargement des logs...</div>
      </div>
    </div>
  </div>
  
  <div class="buttons">
    <button id="clearLogsButton" class="button-danger">Effacer logs</button>
    <button id="resetButton" class="button-secondary">Réinitialiser</button>
    <button id="saveButton" class="button-primary">Enregistrer</button>
  </div>
  
  <script src="../common.js"></script>
  <script src="popup.js"></script>
</body>
</html>