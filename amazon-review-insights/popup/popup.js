/**
 * Amazon Review Insights - Script du popup de configuration
 */

document.addEventListener('DOMContentLoaded', async () => {
  // Importer le logger
  const logger = window.ariLogger?.logger || console;
  
  // Références DOM
  const operationModeToggle = document.getElementById('operationMode');
  const cacheDurationSelect = document.getElementById('cacheDurationSelect');
  const maxParallelAnalysesInput = document.getElementById('maxParallelAnalyses');
  const autoAnalysisEnabledCheckbox = document.getElementById('autoAnalysisEnabled');
  const logLevelSelect = document.getElementById('logLevel');
  const logToConsoleCheckbox = document.getElementById('logToConsole');
  const logToStorageCheckbox = document.getElementById('logToStorage');
  const maxStoredLogsInput = document.getElementById('maxStoredLogs');
  const logsContainer = document.getElementById('logsContainer');
  const logCount = document.getElementById('logCount');
  const logStats = document.getElementById('logStats');
  const modeBanner = document.getElementById('mode-banner');
  const modeIcon = document.getElementById('mode-icon');
  const modeTitle = document.getElementById('mode-title');
  const currentMode = document.getElementById('current-mode');
  const modeDescription = document.getElementById('mode-description');
  
  // Nouvelles références pour l'analyse de marché
  const reviewsPerProductSlider = document.getElementById('reviewsPerProductSlider');
  const reviewsPerProductValue = document.getElementById('reviewsPerProductValue');
  const marketParallelFetchesSlider = document.getElementById('marketParallelFetchesSlider');
  const marketParallelFetchesValue = document.getElementById('marketParallelFetchesValue');
  const marketAutoAnalysisCheckbox = document.getElementById('marketAutoAnalysis');
  const defaultVisualizationSelect = document.getElementById('defaultVisualization');
  
  // Boutons
  const clearLogsButton = document.getElementById('clearLogsButton');
  const resetButton = document.getElementById('resetButton');
  const saveButton = document.getElementById('saveButton');
  
  // Onglets
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');
  
  // Gestionnaire pour les onglets
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Désactiver tous les onglets
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // Activer l'onglet cliqué
      const tabName = button.getAttribute('data-tab');
      button.classList.add('active');
      document.getElementById(`${tabName}-tab`).classList.add('active');
    });
  });
  
  // Actualiser la valeur affichée pour le slider d'avis par produit
  reviewsPerProductSlider.addEventListener('input', () => {
    reviewsPerProductValue.textContent = `${reviewsPerProductSlider.value} avis`;
  });
  
  // Actualiser la valeur affichée pour le slider de requêtes parallèles
  marketParallelFetchesSlider.addEventListener('input', () => {
    marketParallelFetchesValue.textContent = `${marketParallelFetchesSlider.value} requête${marketParallelFetchesSlider.value > 1 ? 's' : ''} en parallèle`;
  });
  
  // Charge la configuration
  async function loadConfig() {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'get_config' });
      
      if (response && response.config) {
        const config = response.config;
        
        // Configuration générale
        operationModeToggle.checked = config.autoAnalysisEnabled || false;
        cacheDurationSelect.value = config.cacheDurationOption || '1_jour';
        maxParallelAnalysesInput.value = config.maxParallelAnalyses || 5;
        autoAnalysisEnabledCheckbox.checked = config.autoAnalysisEnabled || false;
        
        // Configuration des logs
        logLevelSelect.value = config.logLevel || 3;
        logToConsoleCheckbox.checked = config.logToConsole !== false;
        logToStorageCheckbox.checked = config.logToStorage !== false;
        maxStoredLogsInput.value = config.maxStoredLogs || 1000;
        
        // Nouvelles configurations pour l'analyse de marché
        reviewsPerProductSlider.value = config.marketReviewsPerProduct || 300;
        reviewsPerProductValue.textContent = `${reviewsPerProductSlider.value} avis`;
        
        marketParallelFetchesSlider.value = config.marketParallelFetches || 3;
        marketParallelFetchesValue.textContent = `${marketParallelFetchesSlider.value} requête${marketParallelFetchesSlider.value > 1 ? 's' : ''} en parallèle`;
        
        marketAutoAnalysisCheckbox.checked = config.marketAutoAnalysis || false;
        defaultVisualizationSelect.value = config.defaultVisualization || 'scatter';
        
        // Mise à jour du banner de mode
        updateModeBanner(config.autoAnalysisEnabled);
        
        logger.debug('Configuration chargée', config);
      }
    } catch (error) {
      logger.error('Erreur lors du chargement de la configuration', error);
    }
    
    loadLogs();
  }
  
  // Sauvegarde la configuration
  async function saveConfig() {
    try {
      const config = {
        // Configuration générale
        autoAnalysisEnabled: operationModeToggle.checked,
        cacheDurationOption: cacheDurationSelect.value,
        maxParallelAnalyses: parseInt(maxParallelAnalysesInput.value),
        
        // Configuration des logs
        logLevel: parseInt(logLevelSelect.value),
        logToConsole: logToConsoleCheckbox.checked,
        logToStorage: logToStorageCheckbox.checked,
        maxStoredLogs: parseInt(maxStoredLogsInput.value),
        
        // Nouvelles configurations pour l'analyse de marché
        marketReviewsPerProduct: parseInt(reviewsPerProductSlider.value),
        marketParallelFetches: parseInt(marketParallelFetchesSlider.value),
        marketAutoAnalysis: marketAutoAnalysisCheckbox.checked,
        defaultVisualization: defaultVisualizationSelect.value
      };
      
      const response = await chrome.runtime.sendMessage({
        type: 'save_config',
        config
      });
      
      if (response && response.success) {
        logger.info('Configuration sauvegardée', config);
        updateModeBanner(config.autoAnalysisEnabled);
      } else {
        throw new Error('Échec de la sauvegarde');
      }
    } catch (error) {
      logger.error('Erreur lors de la sauvegarde de la configuration', error);
    }
  }
  
  // Met à jour la bannière de mode
  function updateModeBanner(isAutoEnabled) {
    if (isAutoEnabled) {
      modeBanner.style.borderLeftColor = '#4CAF50';
      modeBanner.style.backgroundColor = '#E8F5E9';
      modeIcon.style.backgroundColor = '#4CAF50';
      modeIcon.textContent = 'A';
      modeTitle.textContent = 'Mode Automatique Activé';
      currentMode.textContent = 'mode automatique';
      modeDescription.textContent = 'Les produits sont analysés automatiquement lors de la navigation sur Amazon.';
    } else {
      modeBanner.style.borderLeftColor = '#1a73e8';
      modeBanner.style.backgroundColor = '#e8f0fe';
      modeIcon.style.backgroundColor = '#1a73e8';
      modeIcon.textContent = 'M';
      modeTitle.textContent = 'Mode Manuel Uniquement';
      currentMode.textContent = 'mode manuel';
      modeDescription.textContent = 'Cherchez le badge R à côté des étoiles sur les pages Amazon et cliquez dessus.';
    }
  }
  
  // Charge les logs
  async function loadLogs() {
    try {
      const logs = await chrome.runtime.sendMessage({ type: 'get_logs' });
      
      if (logs && logs.entries) {
        // Mettre à jour le compteur
        logCount.textContent = `(${logs.entries.length})`;
        
        // Statistiques
        let errorCount = 0;
        let warningCount = 0;
        
        logs.entries.forEach(entry => {
          if (entry.level === 1) errorCount++;
          if (entry.level === 2) warningCount++;
        });
        
        logStats.textContent = `${errorCount} erreurs, ${warningCount} avertissements, ${logs.entries.length - errorCount - warningCount} infos`;
        
        // Afficher les logs (limités aux 100 derniers)
        logsContainer.innerHTML = '';
        logs.entries.slice(-100).forEach(entry => {
          const logEntry = document.createElement('div');
          logEntry.className = `log-entry ${entry.level === 1 ? 'log-entry-error' : entry.level === 2 ? 'log-entry-warn' : ''}`;
          
          const badge = document.createElement('span');
          badge.className = `badge badge-level-${entry.level}`;
          badge.textContent = entry.level;
          
          const timestamp = new Date(entry.timestamp).toLocaleTimeString();
          
          logEntry.appendChild(badge);
          logEntry.appendChild(document.createTextNode(` ${timestamp} - ${entry.message}`));
          
          logsContainer.appendChild(logEntry);
        });
      }
    } catch (error) {
      logger.error('Erreur lors du chargement des logs', error);
    }
  }
  
  // Attache les évènements
  function attachEventListeners() {
    operationModeToggle.addEventListener('change', () => {
      updateModeBanner(operationModeToggle.checked);
    });
    
    clearLogsButton.addEventListener('click', async () => {
      try {
        await chrome.runtime.sendMessage({ type: 'clear_logs' });
        loadLogs();
      } catch (error) {
        logger.error('Erreur lors de l\'effacement des logs', error);
      }
    });
    
    resetButton.addEventListener('click', () => {
      loadConfig();
    });
    
    saveButton.addEventListener('click', () => {
      saveConfig();
    });
  }
  
  // Initialisation
  loadConfig();
  attachEventListeners();
});