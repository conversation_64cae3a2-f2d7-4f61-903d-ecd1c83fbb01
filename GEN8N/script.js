// Configuration de l'API OpenAI Assistant
const OPENAI_API_KEY = '********************************************************************************************************************************************************************';
const OPENAI_API_BASE_URL = 'https://api.openai.com/v1';
const ASSISTANT_ID = 'asst_EVjHFlXIaDM1E3xHOqzJxsXs';


// Génération du workflow avec OpenAI Assistant
async function generateWorkflow(prompt) {
    try {
        // 1. Create a Thread
        console.log("Creating a new thread...");
        const threadResponse = await fetch(`${OPENAI_API_BASE_URL}/threads`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${OPENAI_API_KEY}`,
                "OpenAI-Beta": "assistants=v2",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({}) // Empty body to create an empty thread
        });

        if (!threadResponse.ok) {
            const errorData = await threadResponse.json();
            throw new Error(`Error creating thread: ${threadResponse.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        const threadData = await threadResponse.json();
        const threadId = threadData.id;
        console.log("Thread created with ID:", threadId);

        // 2. Add a Message to the Thread
        console.log("Adding message to thread...");
        const messageResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadId}/messages`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${OPENAI_API_KEY}`,
                "OpenAI-Beta": "assistants=v2",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                role: "user",
                content: prompt
            })
        });

        if (!messageResponse.ok) {
            const errorData = await messageResponse.json();
            throw new Error(`Error adding message to thread: ${messageResponse.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }
        console.log("Message added to thread.");

        // 3. Create a Run
        console.log("Creating a run...");
        const runResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadId}/runs`, {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${OPENAI_API_KEY}`,
                "OpenAI-Beta": "assistants=v2",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                assistant_id: ASSISTANT_ID
            })
        });

        if (!runResponse.ok) {
            const errorData = await runResponse.json();
            throw new Error(`Error creating run: ${runResponse.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        const runData = await runResponse.json();
        const runId = runData.id;
        console.log("Run created with ID:", runId);

        // 4. Poll the Run status
        console.log("Polling run status...");
        let runStatus = runData.status;
        while (runStatus !== 'completed' && runStatus !== 'failed' && runStatus !== 'cancelled' && runStatus !== 'expired') {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for 1 second
            const statusResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadId}/runs/${runId}`, {
                method: "GET",
                headers: {
                    "Authorization": `Bearer ${OPENAI_API_KEY}`,
                    "OpenAI-Beta": "assistants=v2",
                }
            });

            if (!statusResponse.ok) {
                const errorData = await statusResponse.json();
                throw new Error(`Error polling run status: ${statusResponse.status} - ${errorData.message || JSON.stringify(errorData)}`);
            }

            const statusData = await statusResponse.json();
            runStatus = statusData.status;
            console.log("Run status:", runStatus);
        }

        if (runStatus !== 'completed') {
             const statusResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadId}/runs/${runId}`, {
                method: "GET",
                headers: {
                    "Authorization": `Bearer ${OPENAI_API_KEY}`,
                    "OpenAI-Beta": "assistants=v2",
                }
            });
            const statusData = await statusResponse.json();
            throw new Error(`Run did not complete successfully. Status: ${runStatus}. Error: ${JSON.stringify(statusData.last_error)}`);
        }

        // 5. Retrieve Messages
        console.log("Run completed. Retrieving messages...");
        const messagesResponse = await fetch(`${OPENAI_API_BASE_URL}/threads/${threadId}/messages`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${OPENAI_API_KEY}`,
                "OpenAI-Beta": "assistants=v2",
            }
        });

        if (!messagesResponse.ok) {
            const errorData = await messagesResponse.json();
            throw new Error(`Error retrieving messages: ${messagesResponse.status} - ${errorData.message || JSON.stringify(errorData)}`);
        }

        const messagesData = await messagesResponse.json();
        console.log("Messages retrieved:", messagesData);

        // Find the last message from the assistant
        const assistantMessage = messagesData.data.find(msg => msg.run_id === runId && msg.role === 'assistant');

        if (!assistantMessage || !assistantMessage.content || assistantMessage.content.length === 0) {
            throw new Error("No response found from the assistant.");
        }

        // Assuming the assistant's response is the JSON workflow in a code block
        const textContent = assistantMessage.content.find(content => content.type === 'text');

        if (!textContent) {
             throw new Error("Assistant response does not contain text content.");
        }

        const workflowText = textContent.text.value;

        // Extraction du JSON de la réponse
        const jsonMatch = workflowText.match(/```json\n([\s\S]*?)\n```/);
        if (jsonMatch && jsonMatch[1]) {
            const workflowJson = JSON.parse(jsonMatch[1]);
            return workflowJson;
        }

        throw new Error("Impossible d'extraire le JSON de la réponse de l'Assistant.");

    } catch (error) {
        console.error('Erreur lors de la génération:', error);
        throw error;
    }
}

// Gestionnaire d'événements
document.addEventListener('DOMContentLoaded', () => {
    const generateBtn = document.getElementById('generateBtn');
    const promptInput = document.getElementById('promptInput');
    const jsonOutput = document.getElementById('jsonOutput');
    const promptTemplatesContainer = document.getElementById('promptTemplates');
    const n8nDemoContainer = document.getElementById('n8n-demo-component-display');

    // Définition des templates de prompt
    const templates = [
        "Créer un workflow qui lit les nouveaux emails Gmail et les enregistre dans une feuille Google Sheets.",
        "Créer un workflow qui surveille un dossier FTP et envoie une notification Discord lorsqu'un nouveau fichier est ajouté.",
        "Créer un workflow qui planifie une tâche quotidienne pour récupérer des données via une API HTTP et les stocker dans Airtable.",
        "Créer un workflow qui récupère les tweets d'un utilisateur Twitter spécifique et les enregistre dans une base de données MongoDB.",
        "Créer un workflow qui surveille les nouveaux articles d'un flux RSS et envoie un résumé quotidien par email.",
        "Créer un workflow qui automatise la publication d'un article de blog WordPress sur plusieurs plateformes de médias sociaux (Facebook, Twitter, LinkedIn).",
        "Créer un workflow complexe qui intègre plusieurs API, effectue des transformations de données avancées, et gère des scénarios d'erreur robustes.",
        "Concevoir un workflow n8n pour automatiser la gestion des leads entrants à partir d'un formulaire de contact sur un site web, en intégrant des services de CRM et de marketing par email.",
        "Développer un workflow n8n pour traiter les données d'un capteur IoT, en effectuant des analyses en temps réel et en déclenchant des alertes en cas d'anomalies.",
        "Construire un workflow n8n pour automatiser le processus de facturation, en intégrant des services de paiement et en générant des rapports financiers.",
        "Créer un workflow qui surveille les mentions de votre marque sur les réseaux sociaux (Twitter, Reddit) et envoie un rapport quotidien à une équipe Slack, en filtrant les mentions négatives.",
        "Développer un workflow pour synchroniser les contacts entre un CRM (ex: Salesforce) et une plateforme d'email marketing (ex: Mailchimp), en gérant les désabonnements et les mises à jour de profil.",
        "Mettre en place un workflow pour automatiser la création de tâches dans un outil de gestion de projet (ex: Jira, Trello) à partir de nouveaux emails ou de messages spécifiques sur un canal Discord.",
        "Concevoir un workflow pour extraire des données de produits d'un site e-commerce (scraping), les nettoyer, les enrichir avec des informations externes (ex: API de prix), et les importer dans une base de données pour analyse.",
        "Automatiser la publication de contenu sur un blog (WordPress) à partir d'un Google Sheet, y compris la récupération d'images depuis un service de stockage cloud (ex: Google Drive) et la planification des publications."
    ];

    // Création des boutons de template
    templates.forEach(template => {
        const button = document.createElement('button');
        button.textContent = template;
        button.classList.add('template-button');
        button.addEventListener('click', () => {
            promptInput.value = template;
        });
        promptTemplatesContainer.appendChild(button);
    });

    // Add event listener to generate button
    generateBtn.addEventListener('click', async () => {
        const prompt = promptInput.value.trim();
        if (!prompt) return;

        try {
            generateBtn.disabled = true;
            generateBtn.textContent = 'Génération en cours...';

            // Pass only the prompt to the updated generateWorkflow function
            const workflowJson = await generateWorkflow(prompt);

            // Mise à jour de l'interface
            jsonOutput.textContent = JSON.stringify(workflowJson, null, 2);

            // Update the n8n demo component
            const n8nDemoComponent = document.createElement('n8n-demo');
            n8nDemoComponent.setAttribute('data-v-2f4878b1', '');
            n8nDemoComponent.setAttribute('workflow', JSON.stringify(workflowJson));
            n8nDemoComponent.setAttribute('collapseformobile', 'false');
            n8nDemoComponent.setAttribute('clicktointeract', 'true');
            n8nDemoComponent.setAttribute('hidecanvaserrors', 'true');
            n8nDemoComponent.setAttribute('disableinteractivity', 'false');
            n8nDemoComponent.setAttribute('theme', 'light');
            n8nDemoContainer.innerHTML = '';
            n8nDemoContainer.appendChild(n8nDemoComponent);

        } catch (error) {
            jsonOutput.textContent = `Erreur: ${error.message}`;
        } finally {
            generateBtn.disabled = false;
            generateBtn.textContent = 'Générer le Workflow';
        }
    });
});
