{"nodes": [{"parameters": {}, "name": "Shopify Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 200]}, {"parameters": {"operation": "on", "spreadsheetId": "{{secrets.googleSheets.spreadsheetId}}", "range": "Sheet1!A:Z", "pollingInterval": 60000}, "name": "Google Sheets Monitor", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [100, 400], "credentials": {"googleSheetsApi": "{{secrets.googleSheets.credentials}}"}}, {"parameters": {"url": "https://api.twitter.com/2/tweets/search/recent", "method": "GET", "headers": {"Authorization": "Bearer {{secrets.twitter.bearerToken}}"}, "query": "query={{secrets.twitter.searchQuery}}", "pollingInterval": 3600000}, "name": "Twitter Mentions Monitor", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [100, 600]}, {"parameters": {"email": "{{secrets.email.email}}", "password": "{{secrets.email.password}}", "pollingInterval": 3600000}, "name": "Email Monitor", "type": "n8n-nodes-base.imap", "typeVersion": 1, "position": [100, 800], "credentials": {"imap": "{{secrets.email.credentials}}"}}, {"parameters": {"apiKey": "{{secrets.emailValidator.apiKey}}"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [300, 300]}, {"parameters": {"apiKey": "{{secrets.geoApi.apiKey}}"}, "name": "Geolocation Enricher", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"apiKey": "{{secrets.openai.apiKey}}"}, "name": "Sentiment Analyzer", "type": "n8n-nodes-base.openai", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"leadScoringLogic": "complexLeadScoring"}, "name": "Lead Scorer", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"deduplicationFields": ["email", "phone"]}, "name": "Deduplicator", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [1100, 300]}, {"parameters": {"leadScoringThresholds": {"A": 80, "B": 50, "C": 0}}, "name": "Lead Router", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1300, 300]}, {"parameters": {"apiKey": "{{secrets.hubspot.apiKey}}", "operation": "createOrUpdate"}, "name": "HubSpot CRM", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [1500, 200], "credentials": {"hubspotApi": "{{secrets.hubspot.credentials}}"}}, {"parameters": {"apiKey": "{{secrets.mailchimp.apiKey}}", "listId": "{{secrets.mailchimp.listId}}"}, "name": "Mailchimp Segmentation", "type": "n8n-nodes-base.mailchimp", "typeVersion": 1, "position": [1500, 400], "credentials": {"mailchimpApi": "{{secrets.mailchimp.credentials}}"}}, {"parameters": {"integrationToken": "{{secrets.notion.integrationToken}}", "databaseId": "{{secrets.notion.databaseId}}"}, "name": "Notion Task Creator", "type": "n8n-nodes-base.notion", "typeVersion": 1, "position": [1500, 600], "credentials": {"notionApi": "{{secrets.notion.credentials}}"}}, {"parameters": {"webhookUrl": "{{secrets.slack.webhookUrl}}"}, "name": "Slack Notification", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [1700, 200], "credentials": {"slackApi": "{{secrets.slack.credentials}}"}}, {"parameters": {"to": "{{lead.email}}", "subject": "New Lead Alert", "html": "Lead Details: {{lead}}"}, "name": "Sales Email", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1700, 400], "credentials": {"gmailOAuth2": "{{secrets.email.credentials}}"}}, {"parameters": {"phoneNumber": "{{lead.phone}}", "message": "Urgent Lead Alert: {{lead}}"}, "name": "SMS Notification", "type": "n8n-nodes-base.twilio", "typeVersion": 1, "position": [1700, 600], "credentials": {"twilioApi": "{{secrets.sms.credentials}}"}}, {"parameters": {"databaseUrl": "{{secrets.postgres.databaseUrl}}"}, "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1900, 300], "credentials": {"postgresdb": "{{secrets.postgres.credentials}}"}}, {"parameters": {"pollingInterval": 86400000}, "name": "Daily Metrics Generator", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [2100, 300]}, {"parameters": {"template": "weeklyReportTemplate"}, "name": "Weekly Report Generator", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [2300, 300]}, {"parameters": {"webhookUrl": "{{secrets.dashboard.webhookUrl}}"}, "name": "Dashboard Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [2500, 300]}, {"parameters": {"retryCount": 3, "retryInterval": 10000}, "name": "Retry Mechanism", "type": "n8n-nodes-base.retry", "typeVersion": 1, "position": [300, 500]}, {"parameters": {"fallbackWorkflowId": "{{secrets.fallback.workflowId}}"}, "name": "Fallback Workflow", "type": "n8n-nodes-base.executeSubWorkflow", "typeVersion": 1, "position": [1700, 800]}, {"parameters": {"conditions": {"combinator": "and", "conditions": [{"operator": {"type": "boolean", "operation": "equals", "name": "filter.operator.equals"}, "leftValue": "={{$json[\"isCriticalError\"]}}", "rightValue": "true"}]}, "options": {}}, "name": "Critical Error <PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1900, 500]}, {"parameters": {"to": "{{secrets.adminEmail.email}}", "subject": "Critical Workflow Failure", "html": "Critical error occurred. See logs for details."}, "name": "Admin <PERSON><PERSON>", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [2100, 500], "credentials": {"gmailOAuth2": "{{secrets.adminEmail.credentials}}"}}], "connections": {"Shopify Webhook Trigger": {"main": [[{"node": "Retry Mechanism", "type": "main", "index": 0}]]}, "Google Sheets Monitor": {"main": [[{"node": "Retry Mechanism", "type": "main", "index": 0}]]}, "Twitter Mentions Monitor": {"main": [[{"node": "Retry Mechanism", "type": "main", "index": 0}]]}, "Email Monitor": {"main": [[{"node": "Retry Mechanism", "type": "main", "index": 0}]]}, "Retry Mechanism": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Email Validator": {"main": [[{"node": "Geolocation Enricher", "type": "main", "index": 0}]]}, "Geolocation Enricher": {"main": [[{"node": "Sentiment Analyzer", "type": "main", "index": 0}]]}, "Sentiment Analyzer": {"main": [[{"node": "Lead Scorer", "type": "main", "index": 0}]]}, "Lead Scorer": {"main": [[{"node": "Deduplicator", "type": "main", "index": 0}]]}, "Deduplicator": {"main": [[{"node": "Lead Router", "type": "main", "index": 0}]]}, "Lead Router": {"A": [[{"node": "HubSpot CRM", "type": "main", "index": 0}, {"node": "Slack Notification", "type": "main", "index": 0}]], "B": [[{"node": "HubSpot CRM", "type": "main", "index": 0}, {"node": "Mailchimp Segmentation", "type": "main", "index": 0}]], "C": [[{"node": "HubSpot CRM", "type": "main", "index": 0}, {"node": "Notion Task Creator", "type": "main", "index": 0}]]}, "HubSpot CRM": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Mailchimp Segmentation": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Notion Task Creator": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Slack Notification": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Sales Email": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "SMS Notification": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Postgres Logger": {"main": [[{"node": "Daily Metrics Generator", "type": "main", "index": 0}]]}, "Daily Metrics Generator": {"main": [[{"node": "Weekly Report Generator", "type": "main", "index": 0}]]}, "Weekly Report Generator": {"main": [[{"node": "Dashboard Webhook", "type": "main", "index": 0}]]}, "Critical Error Handler": {"true": [[{"node": "Admin <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Fallback Workflow": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "credentials": {"secrets.googleSheets": {"spreadsheetId": "YOUR_GOOGLE_SHEETS_ID", "credentials": {"type": "googleSheetsApi", "data": {"clientId": "YOUR_GOOGLE_SHEETS_CLIENT_ID", "clientSecret": "YOUR_GOOGLE_SHEETS_CLIENT_SECRET", "refreshToken": "YOUR_GOOGLE_SHEETS_REFRESH_TOKEN"}}}, "secrets.twitter": {"bearerToken": "YOUR_TWITTER_BEARER_TOKEN", "searchQuery": "#ecommerce OR #leadgeneration"}, "secrets.email": {"email": "YOUR_EMAIL", "password": "YOUR_EMAIL_PASSWORD", "credentials": {"type": "imap", "data": {"host": "YOUR_IMAP_HOST", "port": 993, "username": "YOUR_EMAIL", "password": "YOUR_EMAIL_PASSWORD", "ssl": true}}}, "secrets.emailValidator": {"apiKey": "YOUR_EMAIL_VALIDATOR_API_KEY"}, "secrets.geoApi": {"apiKey": "YOUR_GEOLOCATION_API_KEY"}, "secrets.openai": {"apiKey": "YOUR_OPENAI_API_KEY"}, "secrets.hubspot": {"apiKey": "YOUR_HUBSPOT_API_KEY", "credentials": {"type": "hubspotApi", "data": {"apiKey": "YOUR_HUBSPOT_API_KEY"}}}, "secrets.mailchimp": {"apiKey": "YOUR_MAILCHIMP_API_KEY", "listId": "YOUR_MAILCHIMP_LIST_ID", "credentials": {"type": "mailchimpApi", "data": {"apiKey": "YOUR_MAILCHIMP_API_KEY"}}}, "secrets.notion": {"integrationToken": "YOUR_NOTION_INTEGRATION_TOKEN", "databaseId": "YOUR_NOTION_DATABASE_ID", "credentials": {"type": "notionApi", "data": {"integrationToken": "YOUR_NOTION_INTEGRATION_TOKEN"}}}, "secrets.slack": {"webhookUrl": "YOUR_SLACK_WEBHOOK_URL", "credentials": {"type": "slackApi", "data": {"webhookUrl": "YOUR_SLACK_WEBHOOK_URL"}}}, "secrets.sms": {"credentials": {"type": "t<PERSON><PERSON><PERSON><PERSON>", "data": {"accountSid": "YOUR_TWILIO_ACCOUNT_SID", "authToken": "YOUR_TWILIO_AUTH_TOKEN"}}}, "secrets.postgres": {"databaseUrl": "postgres://user:password@host:port/database", "credentials": {"type": "postgresdb", "data": {"host": "YOUR_POSTGRES_HOST", "port": 5432, "username": "YOUR_POSTGRES_USER", "password": "YOUR_POSTGRES_PASSWORD", "database": "YOUR_POSTGRES_DATABASE"}}}, "secrets.adminEmail": {"email": "YOUR_ADMIN_EMAIL", "credentials": {"type": "gmailOAuth2", "data": {"clientId": "YOUR_ADMIN_GMAIL_CLIENT_ID", "clientSecret": "YOUR_ADMIN_GMAIL_CLIENT_SECRET", "refreshToken": "YOUR_ADMIN_GMAIL_REFRESH_TOKEN"}}}, "secrets.fallback": {"workflowId": "YOUR_FALLBACK_WORKFLOW_ID"}, "secrets.dashboard": {"webhookUrl": "YOUR_DASHBOARD_WEBHOOK_URL"}}}