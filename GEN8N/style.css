:root {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --background-color: #f7f7f7;
    --text-color: #2d3436;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 2.5rem;
}

h2 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
}

.input-section {
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

textarea {
    width: 100%;
    height: 150px;
    padding: 1rem;
    border: 2px solid var(--secondary-color);
    border-radius: 8px;
    resize: vertical;
    font-size: 1rem;
    box-sizing: border-box; /* Inclure le padding et la bordure dans la largeur */
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    align-self: flex-start; /* Aligner le bouton à gauche */
}

button:hover {
    background-color: #ff5252;
}

.model-selection {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.model-selection label {
    font-weight: bold;
    white-space: nowrap; /* Empêche le texte de se casser sur plusieurs lignes */
}

.model-selection select {
    flex-grow: 1; /* Permet au select de prendre l'espace disponible */
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    background-color: white;
    cursor: pointer;
}

.prompt-templates {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Responsive grid */
    gap: 10px;
    margin-bottom: 1rem;
}

.prompt-templates .template-button {
    background-color: #6c757d; /* Couleur différente pour les templates */
    padding: 0.5rem 1rem; /* Reduced padding */
    font-size: 0.85rem; /* Slightly smaller font */
    border-radius: 6px;
    white-space: normal; /* Allow text to wrap */
    text-align: left; /* Align text to left */
    height: auto; /* Adjust height based on content */
    min-height: 40px; /* Minimum height for readability */
}

.prompt-templates .template-button:hover {
    background-color: #5a6268;
}

.output-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.visualization-container, .json-container {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#workflowVisualization {
    width: 100%;
    height: 0px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f0f0f0; /* Fond pour la visualisation */
}

.n8n-demo-display-container {
    width: 100%;
    height: 600px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

#n8n-demo-component-display {
    width: 100%;
    height: 100%;
}

#jsonOutput {
    background-color: #2d3436;
    color: #fff;
    padding: 1rem;
    border-radius: 4px;
    overflow: auto; /* Utiliser auto pour les barres de défilement si nécessaire */
    font-family: 'Courier New', Courier, monospace;
    white-space: pre-wrap;
    height: 500px;
    margin: 0;
    box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .output-section {
        grid-template-columns: 1fr; /* Une seule colonne sur les petits écrans */
    }
}
