// Script de test pour vérifier le fonctionnement de l'outil
const fs = require('fs').promises; // Importe le module fs pour la lecture de fichiers

async function testGenerateWorkflow() {
    console.log("Démarrage du test...");

    // Exemple de prompt pour le test
    const testPrompt = "Créer un workflow n8n qui envoie un email lorsqu'un fichier est téléchargé.";

    try {
        // Lire la documentation n8nDoc.md
        const documentation = await fs.readFile('GEN8N/n8nDoc.md', 'utf8');
        console.log("Documentation n8n lue avec succès.");

        // Construire le prompt complet
        const fullPrompt = `Vous êtes un expert en création de workflows n8n. Utilisez la documentation n8n suivante comme référence pour générer des workflows n8n au format JSON.
        Documentation n8n:
        ${documentation}

        Basé sur cette demande: "${testPrompt}"
        Générez un workflow n8n au format JSON qui répond à ce besoin. Le JSON doit être valide et suivre la structure n8n standard avec les nœuds et connexions appropriés.`;

        // Simuler l'appel à l'API LLM
        console.log("Envoi du prompt à l'API (simulation)...");
        const fakeApiResponse = `{
            "data": {
                "id": "1234",
                "name": "Workflow Test",
                "active": true,
                "nodes": [
                    {
                        "parameters": {},
                        "name": "Start",
                        "type": "n8n-nodes-base.start",
                        "typeVersion": 1,
                        "position": [200, 300]
                    },
                    {
                        "parameters": {
                            "url": "https://example.com/upload",
                            "method": "GET"
                        },
                        "name": "HTTP Request",
                        "type": "n8n-nodes-base.httpRequest",
                        "typeVersion": 1,
                        "position": [400, 300]
                    },
                    {
                        "parameters": {
                            "sendTo": "<EMAIL>",
                            "subject": "File Uploaded",
                            "message": "A new file has been uploaded."
                        },
                        "name": "Send Email",
                        "type": "n8n-nodes-base.emailSend",
                        "typeVersion": 1,
                        "position": [600, 300]
                    }
                ],
                "connections": {
                    "Start": {
                        "main": [
                            [
                                {
                                    "node": "HTTP Request",
                                    "type": "main",
                                    "index": 0
                                }
                            ]
                        ]
                    },
                    "HTTP Request": {
                        "main": [
                            [
                                {
                                    "node": "Send Email",
                                    "type": "main",
                                    "index": 0
                                }
                            ]
                        ]
                    }
                }
            }
        }`;

        // Simuler la réponse de l'API
        const workflowJson = JSON.parse(fakeApiResponse);

        // Vérifier que le JSON est valide
        console.log("Vérification du JSON généré...");
        if (workflowJson && workflowJson.data && workflowJson.data.nodes) {
            console.log("Test réussi : Le workflow a été généré avec succès !");
            console.log(JSON.stringify(workflowJson, null, 2));
        } else {
            console.error("Test échoué : Le JSON généré est invalide.");
        }
    } catch (error) {
        console.error("Erreur lors du test :", error);
    }
}

// Exécuter le script de test
testGenerateWorkflow();